"use client"

import { useEffect, useCallback, useRef } from 'react';
import { Event, TimelineState } from '@/lib/types';

export interface KeyboardShortcuts {
  // Timeline Navigation
  'ArrowLeft': () => void;
  'ArrowRight': () => void;
  'ArrowUp': () => void;
  'ArrowDown': () => void;
  'Home': () => void;
  'End': () => void;
  'PageUp': () => void;
  'PageDown': () => void;
  
  // Event Navigation
  'Enter': () => void;
  'Space': () => void;
  'Escape': () => void;
  'Tab': () => void;
  'Shift+Tab': () => void;
  
  // Zoom Controls
  '+': () => void;
  '=': () => void;
  '-': () => void;
  '0': () => void;
  
  // Quick Actions
  'f': () => void; // Focus search
  'g': () => void; // Toggle globe
  'r': () => void; // Random event
  'h': () => void; // Help/shortcuts
  '?': () => void; // Help/shortcuts
  
  // Number keys for quick navigation
  '1': () => void;
  '2': () => void;
  '3': () => void;
  '4': () => void;
  '5': () => void;
}

export interface UseKeyboardNavigationProps {
  events: Event[];
  selectedEvent: Event | null;
  timelineState: TimelineState;
  onEventSelect: (event: Event) => void;
  onNavigateLeft: () => void;
  onNavigateRight: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomToFit: () => void;
  onToggleGlobe?: () => void;
  onFocusSearch?: () => void;
  onShowHelp?: () => void;
  onRandomEvent?: () => void;
  onCloseModal?: () => void;
  enabled?: boolean;
}

export function useKeyboardNavigation({
  events,
  selectedEvent,
  timelineState,
  onEventSelect,
  onNavigateLeft,
  onNavigateRight,
  onZoomIn,
  onZoomOut,
  onZoomToFit,
  onToggleGlobe,
  onFocusSearch,
  onShowHelp,
  onRandomEvent,
  onCloseModal,
  enabled = true,
}: UseKeyboardNavigationProps) {
  const isModalOpen = useRef(false);
  const lastFocusedElement = useRef<HTMLElement | null>(null);

  // Get current event index
  const getCurrentEventIndex = useCallback(() => {
    if (!selectedEvent) return -1;
    return events.findIndex(event => event.id === selectedEvent.id);
  }, [events, selectedEvent]);

  // Navigate to next event
  const navigateToNextEvent = useCallback(() => {
    const currentIndex = getCurrentEventIndex();
    if (currentIndex < events.length - 1) {
      onEventSelect(events[currentIndex + 1]);
    }
  }, [getCurrentEventIndex, events, onEventSelect]);

  // Navigate to previous event
  const navigateToPreviousEvent = useCallback(() => {
    const currentIndex = getCurrentEventIndex();
    if (currentIndex > 0) {
      onEventSelect(events[currentIndex - 1]);
    }
  }, [getCurrentEventIndex, events, onEventSelect]);

  // Navigate to first event
  const navigateToFirstEvent = useCallback(() => {
    if (events.length > 0) {
      onEventSelect(events[0]);
    }
  }, [events, onEventSelect]);

  // Navigate to last event
  const navigateToLastEvent = useCallback(() => {
    if (events.length > 0) {
      onEventSelect(events[events.length - 1]);
    }
  }, [events, onEventSelect]);

  // Navigate to random event
  const navigateToRandomEvent = useCallback(() => {
    if (events.length > 0) {
      const randomIndex = Math.floor(Math.random() * events.length);
      onEventSelect(events[randomIndex]);
    }
  }, [events, onEventSelect]);

  // Jump to event by number (1-5 for first 5 events)
  const jumpToEventByNumber = useCallback((number: number) => {
    const index = number - 1;
    if (index >= 0 && index < events.length) {
      onEventSelect(events[index]);
    }
  }, [events, onEventSelect]);

  // Define keyboard shortcuts
  const shortcuts: Partial<KeyboardShortcuts> = {
    // Timeline Navigation
    'ArrowLeft': onNavigateLeft,
    'ArrowRight': onNavigateRight,
    'ArrowUp': navigateToPreviousEvent,
    'ArrowDown': navigateToNextEvent,
    'Home': navigateToFirstEvent,
    'End': navigateToLastEvent,
    'PageUp': () => {
      // Navigate 5 events back
      const currentIndex = getCurrentEventIndex();
      const targetIndex = Math.max(0, currentIndex - 5);
      if (events[targetIndex]) {
        onEventSelect(events[targetIndex]);
      }
    },
    'PageDown': () => {
      // Navigate 5 events forward
      const currentIndex = getCurrentEventIndex();
      const targetIndex = Math.min(events.length - 1, currentIndex + 5);
      if (events[targetIndex]) {
        onEventSelect(events[targetIndex]);
      }
    },

    // Event Actions
    'Enter': () => {
      if (selectedEvent && !isModalOpen.current) {
        // Open event details
        const eventElement = document.querySelector(`[data-event-id="${selectedEvent.id}"]`) as HTMLElement;
        if (eventElement) {
          eventElement.click();
        }
      }
    },
    'Space': () => {
      if (selectedEvent && !isModalOpen.current) {
        // Open event details
        const eventElement = document.querySelector(`[data-event-id="${selectedEvent.id}"]`) as HTMLElement;
        if (eventElement) {
          eventElement.click();
        }
      }
    },
    'Escape': () => {
      if (onCloseModal) {
        onCloseModal();
      }
    },

    // Zoom Controls
    '+': onZoomIn,
    '=': onZoomIn, // For keyboards where + requires shift
    '-': onZoomOut,
    '0': onZoomToFit,

    // Quick Actions
    'f': () => {
      if (onFocusSearch) {
        onFocusSearch();
      }
    },
    'g': () => {
      if (onToggleGlobe) {
        onToggleGlobe();
      }
    },
    'r': () => {
      if (onRandomEvent) {
        onRandomEvent();
      } else {
        navigateToRandomEvent();
      }
    },
    'h': () => {
      if (onShowHelp) {
        onShowHelp();
      }
    },
    '?': () => {
      if (onShowHelp) {
        onShowHelp();
      }
    },

    // Number keys for quick navigation
    '1': () => jumpToEventByNumber(1),
    '2': () => jumpToEventByNumber(2),
    '3': () => jumpToEventByNumber(3),
    '4': () => jumpToEventByNumber(4),
    '5': () => jumpToEventByNumber(5),
  };

  // Handle keydown events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't handle shortcuts when typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      return;
    }

    // Create key combination string
    let key = event.key;
    if (event.shiftKey && key !== 'Shift') {
      key = `Shift+${key}`;
    }
    if (event.ctrlKey && key !== 'Control') {
      key = `Ctrl+${key}`;
    }
    if (event.altKey && key !== 'Alt') {
      key = `Alt+${key}`;
    }

    // Execute shortcut if it exists
    const shortcut = shortcuts[key as keyof KeyboardShortcuts];
    if (shortcut) {
      event.preventDefault();
      event.stopPropagation();
      shortcut();
    }
  }, [enabled, shortcuts]);

  // Set up event listeners
  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [enabled, handleKeyDown]);

  // Track modal state
  const setModalOpen = useCallback((open: boolean) => {
    isModalOpen.current = open;
    
    if (open) {
      // Store the currently focused element
      lastFocusedElement.current = document.activeElement as HTMLElement;
    } else {
      // Restore focus to the previously focused element
      if (lastFocusedElement.current) {
        lastFocusedElement.current.focus();
        lastFocusedElement.current = null;
      }
    }
  }, []);

  // Return utilities for components to use
  return {
    setModalOpen,
    shortcuts,
    navigateToNextEvent,
    navigateToPreviousEvent,
    navigateToFirstEvent,
    navigateToLastEvent,
    navigateToRandomEvent,
    jumpToEventByNumber,
  };
}
