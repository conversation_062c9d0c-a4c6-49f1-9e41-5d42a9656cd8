import { Region } from '@/lib/types/region';

export const regions: Region[] = [
  {
    id: 'europe',
    name: 'Europe',
    displayName: 'Europe',
    description: 'The European continent, cradle of Western civilization',
    color: '#3B82F6', // Blue
    isVisible: true,
    subregions: [
      {
        id: 'western-europe',
        name: 'Western Europe',
        displayName: 'Western Europe',
        parentRegion: 'europe',
        color: '#60A5FA',
        isVisible: true,
        coordinates: {
          center: { lat: 50.0, lng: 5.0 },
          bounds: { north: 60.0, south: 40.0, east: 15.0, west: -10.0 }
        },
        eventCount: 0
      },
      {
        id: 'southern-europe',
        name: 'Southern Europe',
        displayName: 'Southern Europe',
        parentRegion: 'europe',
        color: '#93C5FD',
        isVisible: true,
        coordinates: {
          center: { lat: 42.0, lng: 15.0 },
          bounds: { north: 47.0, south: 35.0, east: 30.0, west: 5.0 }
        },
        eventCount: 0
      },
      {
        id: 'northern-europe',
        name: 'Northern Europe',
        displayName: 'Northern Europe',
        parentRegion: 'europe',
        color: '#DBEAFE',
        isVisible: true,
        coordinates: {
          center: { lat: 60.0, lng: 15.0 },
          bounds: { north: 70.0, south: 50.0, east: 30.0, west: -10.0 }
        },
        eventCount: 0
      }
    ],
    coordinates: {
      center: { lat: 54.0, lng: 15.0 },
      bounds: { north: 71.0, south: 35.0, east: 40.0, west: -25.0 }
    },
    historicalBoundaries: [],
    eventCount: 0,
    timeRange: { earliest: -3000, latest: 2024 }
  },
  {
    id: 'asia',
    name: 'Asia',
    displayName: 'Asia',
    description: 'The largest continent, home to ancient civilizations',
    color: '#EF4444', // Red
    isVisible: true,
    subregions: [
      {
        id: 'western-asia',
        name: 'Western Asia',
        displayName: 'Western Asia',
        parentRegion: 'asia',
        color: '#F87171',
        isVisible: true,
        coordinates: {
          center: { lat: 35.0, lng: 45.0 },
          bounds: { north: 45.0, south: 25.0, east: 65.0, west: 25.0 }
        },
        eventCount: 0
      },
      {
        id: 'eastern-asia',
        name: 'Eastern Asia',
        displayName: 'Eastern Asia',
        parentRegion: 'asia',
        color: '#FCA5A5',
        isVisible: true,
        coordinates: {
          center: { lat: 35.0, lng: 120.0 },
          bounds: { north: 50.0, south: 20.0, east: 145.0, west: 95.0 }
        },
        eventCount: 0
      }
    ],
    coordinates: {
      center: { lat: 35.0, lng: 100.0 },
      bounds: { north: 80.0, south: -10.0, east: 180.0, west: 25.0 }
    },
    historicalBoundaries: [],
    eventCount: 0,
    timeRange: { earliest: -3000, latest: 2024 }
  },
  {
    id: 'africa',
    name: 'Africa',
    displayName: 'Africa',
    description: 'The birthplace of humanity and ancient civilizations',
    color: '#10B981', // Green
    isVisible: true,
    subregions: [
      {
        id: 'northern-africa',
        name: 'Northern Africa',
        displayName: 'Northern Africa',
        parentRegion: 'africa',
        color: '#34D399',
        isVisible: true,
        coordinates: {
          center: { lat: 25.0, lng: 15.0 },
          bounds: { north: 35.0, south: 15.0, east: 35.0, west: -20.0 }
        },
        eventCount: 0
      }
    ],
    coordinates: {
      center: { lat: 0.0, lng: 20.0 },
      bounds: { north: 37.0, south: -35.0, east: 52.0, west: -18.0 }
    },
    historicalBoundaries: [],
    eventCount: 0,
    timeRange: { earliest: -3000, latest: 2024 }
  },
  {
    id: 'north-america',
    name: 'North America',
    displayName: 'North America',
    description: 'The continent of exploration and revolution',
    color: '#8B5CF6', // Purple
    isVisible: true,
    subregions: [
      {
        id: 'eastern-north-america',
        name: 'Eastern North America',
        displayName: 'Eastern North America',
        parentRegion: 'north-america',
        color: '#A78BFA',
        isVisible: true,
        coordinates: {
          center: { lat: 40.0, lng: -80.0 },
          bounds: { north: 50.0, south: 25.0, east: -65.0, west: -95.0 }
        },
        eventCount: 0
      }
    ],
    coordinates: {
      center: { lat: 45.0, lng: -100.0 },
      bounds: { north: 85.0, south: 15.0, east: -50.0, west: -170.0 }
    },
    historicalBoundaries: [],
    eventCount: 0,
    timeRange: { earliest: -3000, latest: 2024 }
  }
];

/**
 * Get all regions
 */
export function getAllRegions(): Region[] {
  return regions;
}

/**
 * Get region by ID
 */
export function getRegionById(id: string): Region | undefined {
  return regions.find(region => region.id === id);
}

/**
 * Get visible regions
 */
export function getVisibleRegions(): Region[] {
  return regions.filter(region => region.isVisible);
}

/**
 * Get region colors map
 */
export function getRegionColors(): Record<string, string> {
  const colors: Record<string, string> = {};
  regions.forEach(region => {
    colors[region.id] = region.color;
  });
  return colors;
}
