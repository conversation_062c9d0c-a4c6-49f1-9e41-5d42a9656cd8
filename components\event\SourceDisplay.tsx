"use client"

import React, { useState } from 'react';
import { Event } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Separator } from '@/components/ui/separator';
import { 
  ExternalLink, 
  BookOpen, 
  FileText, 
  Globe, 
  Archive, 
  ChevronDown, 
  ChevronUp,
  Star,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SourceDisplayProps {
  event: Event;
  showReliabilityInfo?: boolean;
  maxSources?: number;
  className?: string;
}

export function SourceDisplay({
  event,
  showReliabilityInfo = true,
  maxSources = 10,
  className,
}: SourceDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showAllSources, setShowAllSources] = useState(false);

  const sources = event.sources || [];
  const displayedSources = showAllSources ? sources : sources.slice(0, maxSources);

  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'primary':
        return <BookOpen className="h-4 w-4" />;
      case 'secondary':
        return <FileText className="h-4 w-4" />;
      case 'archaeological':
        return <Archive className="h-4 w-4" />;
      case 'digital':
        return <Globe className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getReliabilityIcon = (reliability: string) => {
    switch (reliability) {
      case 'high':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'medium':
        return <Info className="h-4 w-4 text-yellow-600" />;
      case 'low':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  const getReliabilityColor = (reliability: string) => {
    switch (reliability) {
      case 'high':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'low':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getSourceTypeColor = (type: string) => {
    switch (type) {
      case 'primary':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'secondary':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'archaeological':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200';
      case 'digital':
        return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const sourceCounts = sources.reduce((acc, source) => {
    acc[source.type] = (acc[source.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const reliabilityCounts = sources.reduce((acc, source) => {
    acc[source.reliability] = (acc[source.reliability] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (sources.length === 0) {
    return (
      <div className={cn("text-center py-4", className)}>
        <p className="text-muted-foreground text-sm">No sources available for this event</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Source Summary */}
      <div className="flex flex-wrap gap-2">
        <Badge variant="outline" className="flex items-center gap-1">
          <BookOpen className="h-3 w-3" />
          {sources.length} Sources
        </Badge>
        
        {Object.entries(sourceCounts).map(([type, count]) => (
          <Badge key={type} className={cn("text-xs", getSourceTypeColor(type))}>
            {count} {type}
          </Badge>
        ))}
      </div>

      {/* Reliability Overview */}
      {showReliabilityInfo && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Source Reliability</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(reliabilityCounts).map(([reliability, count]) => (
              <Badge 
                key={reliability} 
                className={cn("text-xs flex items-center gap-1", getReliabilityColor(reliability))}
              >
                {getReliabilityIcon(reliability)}
                {count} {reliability}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <Separator />

      {/* Sources List */}
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-0 h-auto">
            <span className="text-sm font-medium">View Sources</span>
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="space-y-3 mt-3">
          {displayedSources.map((source, index) => (
            <Card key={index} className="border-l-4 border-l-primary/20">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    {getSourceIcon(source.type)}
                    {source.title}
                  </CardTitle>
                  <div className="flex gap-1">
                    <Badge className={cn("text-xs", getSourceTypeColor(source.type))}>
                      {source.type}
                    </Badge>
                    <Badge className={cn("text-xs", getReliabilityColor(source.reliability))}>
                      {source.reliability}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="space-y-2">
                  {source.author && (
                    <p className="text-xs text-muted-foreground">
                      <strong>Author:</strong> {source.author}
                    </p>
                  )}
                  
                  {source.date && (
                    <p className="text-xs text-muted-foreground">
                      <strong>Date:</strong> {source.date}
                    </p>
                  )}
                  
                  {source.description && (
                    <p className="text-xs text-muted-foreground">
                      {source.description}
                    </p>
                  )}
                  
                  {source.url && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 text-xs text-primary hover:text-primary/80"
                      onClick={() => window.open(source.url, '_blank')}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View Source
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}

          {sources.length > maxSources && !showAllSources && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAllSources(true)}
              className="w-full"
            >
              Show {sources.length - maxSources} more sources
            </Button>
          )}

          {showAllSources && sources.length > maxSources && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAllSources(false)}
              className="w-full"
            >
              Show fewer sources
            </Button>
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Source Quality Indicator */}
      {showReliabilityInfo && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Star className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium">Source Quality</span>
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <p>
              <strong>High:</strong> Contemporary accounts, official records, archaeological evidence
            </p>
            <p>
              <strong>Medium:</strong> Near-contemporary sources, scholarly consensus
            </p>
            <p>
              <strong>Low:</strong> Later accounts, disputed sources, limited evidence
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
