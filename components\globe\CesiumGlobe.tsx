"use client"

import React, { useEffect, useRef, useState, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { Event, TimelineState } from '@/lib/types';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Home,
  Loader2
} from 'lucide-react';

// Import Cesium types
import { Cartesian3, Color, HeightReference } from 'cesium';

// Dynamic import of Resium components to avoid SSR issues
const Viewer = dynamic(() => import('resium').then(mod => ({ default: mod.Viewer })), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-muted/20">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  )
});

const Entity = dynamic(() => import('resium').then(mod => ({ default: mod.Entity })), {
  ssr: false
});

const PointGraphics = dynamic(() => import('resium').then(mod => ({ default: mod.PointGraphics })), {
  ssr: false
});

interface CesiumGlobeProps {
  events: Event[];
  selectedEvent?: Event | null;
  timelineState?: TimelineState;
  onEventSelect?: (event: Event | null) => void;
  onRegionSelect?: (region: string) => void;
  className?: string;
  showControls?: boolean;
  showEventMarkers?: boolean;
  showRegionHighlights?: boolean;
}

interface GlobeTooltip {
  content: string;
  x: number;
  y: number;
  visible: boolean;
}

export function CesiumGlobe({
  events,
  selectedEvent,
  timelineState,
  onEventSelect,
  onRegionSelect,
  className,
  showControls = true,
  showEventMarkers = true,
  showRegionHighlights = true,
}: CesiumGlobeProps) {
  const viewerRef = useRef<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [tooltip, setTooltip] = useState<GlobeTooltip>({
    content: '',
    x: 0,
    y: 0,
    visible: false,
  });

  // Filter events that have coordinates
  const eventsWithCoordinates = React.useMemo(() => {
    return events.filter(event => 
      event.location.coordinates && 
      event.location.coordinates.lat && 
      event.location.coordinates.lng
    );
  }, [events]);

  // Handle viewer ready
  const handleViewerReady = useCallback((viewer: any) => {
    if (viewer && viewer.cesiumElement) {
      viewerRef.current = viewer.cesiumElement;
      setIsLoaded(true);
      
      // Configure the viewer
      const cesiumViewer = viewer.cesiumElement;
      
      // Enable terrain depth testing
      cesiumViewer.scene.globe.depthTestAgainstTerrain = true;
      
      // Set up lighting
      cesiumViewer.scene.globe.enableLighting = true;
      
      // Configure camera
      cesiumViewer.camera.setView({
        destination: cesiumViewer.camera.positionCartographic,
      });
    }
  }, []);

  // Handle event click
  const handleEventClick = useCallback((event: Event) => {
    if (onEventSelect) {
      onEventSelect(event);
    }
    
    // Zoom to event location
    if (viewerRef.current && event.location.coordinates) {
      const { lat, lng } = event.location.coordinates;
      viewerRef.current.camera.flyTo({
        destination: Cartesian3.fromDegrees(lng, lat, 1000000),
        duration: 2.0,
      });
    }
  }, [onEventSelect]);

  // Globe control functions
  const handleZoomIn = useCallback(() => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const height = camera.positionCartographic.height;
      camera.zoomIn(height * 0.5);
    }
  }, []);

  const handleZoomOut = useCallback(() => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const height = camera.positionCartographic.height;
      camera.zoomOut(height * 0.5);
    }
  }, []);

  const handleResetView = useCallback(() => {
    if (viewerRef.current) {
      viewerRef.current.camera.flyHome(2.0);
    }
  }, []);

  const handleGoToLocation = useCallback((lat: number, lng: number, height: number = 10000000) => {
    if (viewerRef.current) {
      viewerRef.current.camera.flyTo({
        destination: Cartesian3.fromDegrees(lng, lat, height),
        duration: 2.0,
      });
    }
  }, []);

  // Get event marker color based on category
  const getEventColor = useCallback((event: Event) => {
    const colorMap: Record<string, string> = {
      'Political': '#ef4444', // red
      'Military': '#dc2626', // dark red
      'Cultural': '#8b5cf6', // purple
      'Scientific': '#06b6d4', // cyan
      'Economic': '#eab308', // yellow
      'Religious': '#f97316', // orange
      'Social': '#10b981', // emerald
      'Technological': '#3b82f6', // blue
    };
    
    return colorMap[event.category.primary] || '#6b7280'; // default gray
  }, []);

  // Get event size based on significance
  const getEventSize = useCallback((event: Event) => {
    const sizeMap: Record<string, number> = {
      'major': 15,
      'moderate': 10,
      'minor': 7,
      'reference': 5,
    };
    
    return sizeMap[event.significance] || 8;
  }, []);

  return (
    <div className={cn("relative w-full h-full", className)}>
      {/* Cesium Viewer */}
      <div className="w-full h-full">
        <Viewer
          ref={handleViewerReady}
          full
          animation={false}
          timeline={false}
          homeButton={false}
          sceneModePicker={false}
          baseLayerPicker={false}
          navigationHelpButton={false}
          geocoder={false}
          fullscreenButton={false}
          vrButton={false}
          infoBox={false}
          selectionIndicator={false}
        >
          {/* Event Markers */}
          {showEventMarkers && eventsWithCoordinates.map((event) => {
            if (!event.location.coordinates) return null;

            const { lat, lng } = event.location.coordinates;
            const position = Cartesian3.fromDegrees(lng, lat);

            return (
              <Entity
                key={event.id}
                position={position}
                name={event.title}
                description={event.description.short}
                onClick={() => handleEventClick(event)}
              >
                <PointGraphics
                  pixelSize={getEventSize(event)}
                  color={Color.fromCssColorString(getEventColor(event))}
                  outlineColor={Color.WHITE}
                  outlineWidth={2}
                  heightReference={HeightReference.CLAMP_TO_GROUND}
                  disableDepthTestDistance={Number.POSITIVE_INFINITY}
                />
              </Entity>
            );
          })}
        </Viewer>
      </div>

      {/* Globe Controls */}
      {showControls && (
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <Card className="p-2">
            <CardContent className="p-0 flex flex-col gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                className="h-8 w-8 p-0"
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                className="h-8 w-8 p-0"
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetView}
                className="h-8 w-8 p-0"
                title="Reset View"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleGoToLocation(0, 0)}
                className="h-8 w-8 p-0"
                title="Go Home"
              >
                <Home className="h-4 w-4" />
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Event Info Panel */}
      {selectedEvent && (
        <div className="absolute bottom-4 left-4 max-w-sm">
          <Card>
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <h3 className="font-semibold text-sm">{selectedEvent.title}</h3>
                  <Badge variant="outline" className="text-xs">
                    {selectedEvent.date.start}
                  </Badge>
                </div>
                
                <p className="text-xs text-muted-foreground">
                  {selectedEvent.description.short}
                </p>
                
                <div className="flex items-center gap-2 text-xs">
                  <Badge 
                    variant="secondary" 
                    style={{ backgroundColor: getEventColor(selectedEvent) + '20' }}
                  >
                    {selectedEvent.category.primary}
                  </Badge>
                  
                  <span className="text-muted-foreground">
                    {selectedEvent.location.country}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Loading State */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      )}

      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute z-50 bg-popover text-popover-foreground p-2 rounded-md shadow-md text-xs pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
          }}
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
}
