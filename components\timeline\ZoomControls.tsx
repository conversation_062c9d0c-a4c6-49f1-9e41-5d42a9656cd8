"use client"

import React from 'react';
import { TimelineState } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ZoomControlsProps {
  currentZoom: TimelineState['zoomLevel'];
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomToFit: () => void;
  canZoomIn: boolean;
  canZoomOut: boolean;
  className?: string;
}

export function ZoomControls({
  currentZoom,
  onZoomIn,
  onZoomOut,
  onZoomToFit,
  canZoomIn,
  canZoomOut,
  className,
}: ZoomControlsProps) {
  const zoomLevels: Array<{
    level: TimelineState['zoomLevel'];
    label: string;
    description: string;
  }> = [
    { level: 'century', label: 'Century', description: 'View centuries at a glance' },
    { level: 'decade', label: 'Decade', description: 'View decades in detail' },
    { level: 'year', label: 'Year', description: 'View individual years' },
    { level: 'month', label: 'Month', description: 'View months within a year' },
    { level: 'day', label: 'Day', description: 'View days within a month' },
  ];

  const getCurrentZoomInfo = () => {
    return zoomLevels.find(z => z.level === currentZoom) || zoomLevels[1];
  };

  const currentZoomInfo = getCurrentZoomInfo();

  return (
    <div className={cn("flex flex-col sm:flex-row items-start sm:items-center gap-2", className)}>
      {/* Zoom Level Indicator */}
      <div className="flex items-center gap-1 sm:gap-2">
        <span className="text-xs sm:text-sm text-muted-foreground">Zoom:</span>
        <Badge variant="secondary" className="font-medium text-xs sm:text-sm">
          {currentZoomInfo.label}
        </Badge>
      </div>

      {/* Zoom Controls */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={onZoomOut}
          disabled={!canZoomOut}
          title="Zoom Out"
          className="h-8 w-8 p-0"
        >
          <ZoomOut className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onZoomToFit}
          title="Zoom to Fit"
          className="h-8 w-8 p-0"
        >
          <Maximize2 className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onZoomIn}
          disabled={!canZoomIn}
          title="Zoom In"
          className="h-8 w-8 p-0"
        >
          <ZoomIn className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>
      </div>

      {/* Zoom Level Selector */}
      <div className="hidden lg:flex items-center gap-1">
        {zoomLevels.map((zoom) => (
          <Button
            key={zoom.level}
            variant={currentZoom === zoom.level ? "default" : "ghost"}
            size="sm"
            onClick={() => {
              // This would need to be passed as a prop or handled differently
              // For now, we'll use the existing zoom in/out functions
            }}
            title={zoom.description}
            className="h-8 px-1 sm:px-2 text-xs"
          >
            {zoom.label}
          </Button>
        ))}
      </div>
    </div>
  );
}
