"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { ComposableMap, Geographies, Geography, Marker, ZoomableGroup } from 'react-simple-maps';
import { motion, AnimatePresence } from 'framer-motion';
import { Event, TimelineState } from '@/lib/types';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, RotateCcw, MapPin, Globe } from 'lucide-react';

// World map topojson URL
const geoUrl = "https://raw.githubusercontent.com/deldersveld/topojson/master/world-countries.json";

interface HistoricalMapProps {
  events: Event[];
  selectedEvent: Event | null;
  timelineState: TimelineState;
  onEventSelect: (event: Event) => void;
  onRegionSelect: (region: string) => void;
  className?: string;
  showControls?: boolean;
  showEventMarkers?: boolean;
  showRegionHighlights?: boolean;
}

interface MapTooltip {
  content: string;
  x: number;
  y: number;
  visible: boolean;
}

export function HistoricalMap({
  events,
  selectedEvent,
  timelineState,
  onEventSelect,
  onRegionSelect,
  className,
  showControls = true,
  showEventMarkers = true,
  showRegionHighlights = true,
}: HistoricalMapProps) {
  const [position, setPosition] = useState({ coordinates: [0, 0], zoom: 1 });
  const [tooltip, setTooltip] = useState<MapTooltip>({
    content: '',
    x: 0,
    y: 0,
    visible: false,
  });

  // Filter events that have coordinates
  const eventsWithCoordinates = useMemo(() => {
    return events.filter(event => 
      event.location.coordinates && 
      event.location.coordinates.lat && 
      event.location.coordinates.lng
    );
  }, [events]);

  // Get region event counts for highlighting
  const regionEventCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    events.forEach(event => {
      const region = event.location.region;
      counts[region] = (counts[region] || 0) + 1;
    });
    return counts;
  }, [events]);

  // Get significance color for events
  const getSignificanceColor = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return '#ef4444'; // red-500
      case 'moderate':
        return '#f59e0b'; // amber-500
      case 'minor':
        return '#3b82f6'; // blue-500
      case 'reference':
        return '#6b7280'; // gray-500
      default:
        return '#6b7280';
    }
  };

  // Get region color based on event count
  const getRegionColor = (geo: any) => {
    const countryName = geo.properties.NAME || geo.properties.name;
    const region = getRegionFromCountry(countryName);
    const eventCount = regionEventCounts[region] || 0;
    
    if (!showRegionHighlights || eventCount === 0) {
      return '#f3f4f6'; // gray-100
    }
    
    // Color intensity based on event count
    if (eventCount >= 10) return '#dc2626'; // red-600
    if (eventCount >= 5) return '#ea580c'; // orange-600
    if (eventCount >= 2) return '#d97706'; // amber-600
    return '#65a30d'; // lime-600
  };

  // Simple mapping from country to region (this could be more sophisticated)
  const getRegionFromCountry = (countryName: string): string => {
    const countryToRegion: Record<string, string> = {
      'United Kingdom': 'Europe',
      'France': 'Europe',
      'Germany': 'Europe',
      'Italy': 'Europe',
      'Spain': 'Europe',
      'China': 'Asia',
      'India': 'Asia',
      'Japan': 'Asia',
      'Egypt': 'Africa',
      'United States': 'North America',
      'Canada': 'North America',
      'Mexico': 'North America',
      'Brazil': 'South America',
      'Argentina': 'South America',
      'Australia': 'Oceania',
      // Add more mappings as needed
    };
    return countryToRegion[countryName] || 'Other';
  };

  const handleZoomIn = () => {
    if (position.zoom >= 4) return;
    setPosition(prev => ({ ...prev, zoom: prev.zoom * 1.5 }));
  };

  const handleZoomOut = () => {
    if (position.zoom <= 1) return;
    setPosition(prev => ({ ...prev, zoom: prev.zoom / 1.5 }));
  };

  const handleReset = () => {
    setPosition({ coordinates: [0, 0], zoom: 1 });
  };

  const handleGeographyClick = (geo: any) => {
    const countryName = geo.properties.NAME || geo.properties.name;
    const region = getRegionFromCountry(countryName);
    onRegionSelect(region);
    
    // Show tooltip
    setTooltip({
      content: `${countryName} (${region})`,
      x: 0,
      y: 0,
      visible: true,
    });
    
    setTimeout(() => {
      setTooltip(prev => ({ ...prev, visible: false }));
    }, 2000);
  };

  const handleMarkerClick = (event: Event) => {
    onEventSelect(event);
  };

  return (
    <div className={cn("relative w-full h-full bg-background rounded-lg overflow-hidden", className)}>
      {/* Map Container */}
      <div className="relative w-full h-full">
        <ComposableMap
          projection="geoMercator"
          projectionConfig={{
            scale: 100,
          }}
          className="w-full h-full"
        >
          <ZoomableGroup
            zoom={position.zoom}
            center={position.coordinates as [number, number]}
            onMoveEnd={setPosition}
          >
            <Geographies geography={geoUrl}>
              {({ geographies }) =>
                geographies.map((geo) => (
                  <Geography
                    key={geo.rsmKey}
                    geography={geo}
                    onClick={() => handleGeographyClick(geo)}
                    style={{
                      default: {
                        fill: getRegionColor(geo),
                        stroke: '#e5e7eb',
                        strokeWidth: 0.5,
                        outline: 'none',
                      },
                      hover: {
                        fill: '#3b82f6',
                        stroke: '#1d4ed8',
                        strokeWidth: 1,
                        outline: 'none',
                        cursor: 'pointer',
                      },
                      pressed: {
                        fill: '#1e40af',
                        stroke: '#1e3a8a',
                        strokeWidth: 1,
                        outline: 'none',
                      },
                    }}
                  />
                ))
              }
            </Geographies>

            {/* Event Markers */}
            {showEventMarkers && eventsWithCoordinates.map((event) => (
              <Marker
                key={event.id}
                coordinates={[event.location.coordinates!.lng, event.location.coordinates!.lat]}
                onClick={() => handleMarkerClick(event)}
              >
                <motion.circle
                  r={selectedEvent?.id === event.id ? 8 : 5}
                  fill={getSignificanceColor(event.significance)}
                  stroke="#ffffff"
                  strokeWidth={2}
                  className="cursor-pointer"
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
                {selectedEvent?.id === event.id && (
                  <motion.circle
                    r={12}
                    fill="none"
                    stroke={getSignificanceColor(event.significance)}
                    strokeWidth={2}
                    strokeDasharray="4,4"
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </Marker>
            ))}
          </ZoomableGroup>
        </ComposableMap>

        {/* Map Controls */}
        {showControls && (
          <div className="absolute top-4 right-4 flex flex-col gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={handleZoomIn}
              disabled={position.zoom >= 4}
              className="bg-background/80 backdrop-blur-sm"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleZoomOut}
              disabled={position.zoom <= 1}
              className="bg-background/80 backdrop-blur-sm"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleReset}
              className="bg-background/80 backdrop-blur-sm"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-background/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Globe className="h-4 w-4" />
              Map Legend
            </div>
            
            {showEventMarkers && (
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Event Significance:</div>
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span className="text-xs">Major</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                    <span className="text-xs">Moderate</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span className="text-xs">Minor</span>
                  </div>
                </div>
              </div>
            )}

            {showRegionHighlights && (
              <div className="space-y-1">
                <div className="text-xs text-muted-foreground">Event Density:</div>
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-red-600"></div>
                    <span className="text-xs">High (10+)</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-orange-600"></div>
                    <span className="text-xs">Medium (5-9)</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-amber-600"></div>
                    <span className="text-xs">Low (2-4)</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-3 bg-lime-600"></div>
                    <span className="text-xs">Minimal (1)</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Event Count Badge */}
        <div className="absolute top-4 left-4">
          <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
            <MapPin className="h-3 w-3 mr-1" />
            {eventsWithCoordinates.length} events mapped
          </Badge>
        </div>

        {/* Tooltip */}
        <AnimatePresence>
          {tooltip.visible && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
            >
              <Card className="bg-background/90 backdrop-blur-sm shadow-lg">
                <CardContent className="p-2">
                  <div className="text-sm font-medium">{tooltip.content}</div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
