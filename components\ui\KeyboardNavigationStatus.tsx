"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Event } from '@/lib/types';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Keyboard, Navigation, Eye } from 'lucide-react';

interface KeyboardNavigationStatusProps {
  selectedEvent: Event | null;
  totalEvents: number;
  currentIndex: number;
  isKeyboardActive: boolean;
  className?: string;
}

export function KeyboardNavigationStatus({
  selectedEvent,
  totalEvents,
  currentIndex,
  isKeyboardActive,
  className,
}: KeyboardNavigationStatusProps) {
  const [showStatus, setShowStatus] = useState(false);
  const [lastAction, setLastAction] = useState<string | null>(null);

  useEffect(() => {
    if (isKeyboardActive) {
      setShowStatus(true);
      setLastAction('Keyboard navigation active');
      
      const timer = setTimeout(() => {
        setShowStatus(false);
        setLastAction(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isKeyboardActive, selectedEvent]);

  if (!showStatus && !isKeyboardActive) return null;

  return (
    <div className={cn("fixed bottom-4 left-4 z-40 pointer-events-none", className)}>
      <AnimatePresence>
        {(showStatus || isKeyboardActive) && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="bg-background/90 backdrop-blur-sm border rounded-lg p-3 shadow-lg max-w-xs"
          >
            <div className="flex items-center gap-2 mb-2">
              <Keyboard className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Keyboard Navigation</span>
              <Badge variant="secondary" className="text-xs">
                Active
              </Badge>
            </div>

            {selectedEvent && (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Navigation className="h-3 w-3" />
                  <span>Event {currentIndex + 1} of {totalEvents}</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <Eye className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium truncate">
                    {selectedEvent.title}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {selectedEvent.date.display}
                </div>
              </div>
            )}

            {lastAction && (
              <div className="mt-2 pt-2 border-t">
                <div className="text-xs text-muted-foreground">
                  {lastAction}
                </div>
              </div>
            )}

            <div className="mt-2 pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                Press <Badge variant="outline" className="px-1 py-0 text-xs">H</Badge> for help
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
