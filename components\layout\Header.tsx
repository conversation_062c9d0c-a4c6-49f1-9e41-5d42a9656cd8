"use client"

import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { ThemeToggle } from '@/components/theme-toggle';
import { SearchBar } from '@/components/search/SearchBar';
import { 
  Menu, 
  Clock, 
  Globe, 
  Info, 
  Github, 
  Twitter,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { siteConfig } from '@/lib/config/site';
import { Event } from '@/lib/types';

interface HeaderProps {
  events?: Event[];
  onEventSelect?: (event: Event) => void;
  onSearchChange?: (query: string, results: any[]) => void;
  className?: string;
}

export function Header({ 
  events = [], 
  onEventSelect, 
  onSearchChange,
  className 
}: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigationItems = [
    {
      label: 'Timeline',
      href: '#timeline',
      icon: Clock,
      description: 'Explore historical events'
    },
    {
      label: 'Globe',
      href: '#globe',
      icon: Globe,
      description: 'Interactive 3D world view'
    },
    {
      label: 'About',
      href: '#about',
      icon: Info,
      description: 'Learn more about this project'
    }
  ];

  const socialLinks = [
    {
      label: 'GitHub',
      href: siteConfig.links.github,
      icon: Github
    },
    {
      label: 'Twitter',
      href: siteConfig.links.twitter,
      icon: Twitter
    }
  ];

  return (
    <header className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand */}
          <motion.div 
            className="flex items-center space-x-3"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-primary to-primary/80 text-primary-foreground">
              <Sparkles className="h-4 w-4" />
            </div>
            <div className="hidden sm:block">
              <Link href="/" className="flex flex-col">
                <span className="text-lg font-bold leading-none">
                  {siteConfig.name.split(' ').slice(0, 2).join(' ')}
                </span>
                <span className="text-xs text-muted-foreground leading-none">
                  {siteConfig.name.split(' ').slice(2).join(' ')}
                </span>
              </Link>
            </div>
            <div className="sm:hidden">
              <Link href="/" className="text-lg font-bold">
                IHT
              </Link>
            </div>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigationItems.map((item) => (
              <Link
                key={item.label}
                href={item.href}
                className="flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                <item.icon className="h-4 w-4" />
                <span>{item.label}</span>
              </Link>
            ))}
          </nav>

          {/* Search Bar - Hidden on mobile, shown on tablet+ */}
          <div className="hidden lg:flex flex-1 max-w-md mx-6">
            {events.length > 0 && (
              <SearchBar
                events={events}
                onEventSelect={onEventSelect || (() => {})}
                onSearchChange={onSearchChange}
                placeholder="Search historical events..."
                className="w-full"
              />
            )}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            {/* Social Links - Desktop only */}
            <div className="hidden lg:flex items-center space-x-1">
              {socialLinks.map((link) => (
                <Button
                  key={link.label}
                  variant="ghost"
                  size="sm"
                  asChild
                  className="h-8 w-8 p-0"
                >
                  <Link href={link.href} target="_blank" rel="noopener noreferrer">
                    <link.icon className="h-4 w-4" />
                    <span className="sr-only">{link.label}</span>
                  </Link>
                </Button>
              ))}
            </div>

            {/* Theme Toggle */}
            <ThemeToggle />

            {/* Mobile Menu */}
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden h-8 w-8 p-0"
                >
                  <Menu className="h-4 w-4" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <div className="flex flex-col space-y-6 mt-6">
                  {/* Mobile Search */}
                  {events.length > 0 && (
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Search Events</h3>
                      <SearchBar
                        events={events}
                        onEventSelect={(event) => {
                          onEventSelect?.(event);
                          setIsMobileMenuOpen(false);
                        }}
                        onSearchChange={onSearchChange}
                        placeholder="Search historical events..."
                        className="w-full"
                      />
                    </div>
                  )}

                  {/* Mobile Navigation */}
                  <nav className="space-y-2">
                    <h3 className="text-sm font-medium">Navigation</h3>
                    {navigationItems.map((item) => (
                      <Link
                        key={item.label}
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="flex items-center space-x-3 p-2 rounded-md hover:bg-accent transition-colors"
                      >
                        <item.icon className="h-4 w-4" />
                        <div>
                          <div className="text-sm font-medium">{item.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.description}
                          </div>
                        </div>
                      </Link>
                    ))}
                  </nav>

                  {/* Mobile Social Links */}
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Connect</h3>
                    <div className="flex space-x-2">
                      {socialLinks.map((link) => (
                        <Button
                          key={link.label}
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={link.href} target="_blank" rel="noopener noreferrer">
                            <link.icon className="h-4 w-4 mr-2" />
                            {link.label}
                          </Link>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* App Info */}
                  <div className="pt-4 border-t space-y-2">
                    <h3 className="text-sm font-medium">About</h3>
                    <p className="text-xs text-muted-foreground">
                      {siteConfig.description}
                    </p>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
