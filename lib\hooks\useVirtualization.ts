"use client"

import { useState, useEffect, useMemo, useCallback } from 'react';
import { Event } from '@/lib/types';

interface VirtualizationOptions {
  containerHeight: number;
  itemHeight: number;
  overscan?: number;
  enabled?: boolean;
}

interface VirtualizedItem {
  index: number;
  event: Event;
  top: number;
  height: number;
  isVisible: boolean;
}

export function useVirtualization(
  events: Event[],
  scrollTop: number,
  options: VirtualizationOptions
) {
  const { containerHeight, itemHeight, overscan = 5, enabled = true } = options;

  const virtualizedItems = useMemo(() => {
    if (!enabled || events.length === 0) {
      return events.map((event, index) => ({
        index,
        event,
        top: index * itemHeight,
        height: itemHeight,
        isVisible: true,
      }));
    }

    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      events.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );

    const items: VirtualizedItem[] = [];
    
    for (let i = startIndex; i <= endIndex; i++) {
      items.push({
        index: i,
        event: events[i],
        top: i * itemHeight,
        height: itemHeight,
        isVisible: true,
      });
    }

    return items;
  }, [events, scrollTop, containerHeight, itemHeight, overscan, enabled]);

  const totalHeight = events.length * itemHeight;
  const visibleRange = { start: virtualizedItems[0]?.index || 0, end: virtualizedItems[virtualizedItems.length - 1]?.index || 0 };

  return {
    virtualizedItems,
    totalHeight,
    visibleRange,
    isVirtualized: enabled,
  };
}

// Performance monitoring hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    eventCount: 0,
    memoryUsage: 0,
    fps: 0,
  });

  const measureRenderTime = useCallback((startTime: number) => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    setMetrics(prev => ({
      ...prev,
      renderTime: Math.round(renderTime * 100) / 100,
    }));
  }, []);

  const updateEventCount = useCallback((count: number) => {
    setMetrics(prev => ({ ...prev, eventCount: count }));
  }, []);

  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      setMetrics(prev => ({
        ...prev,
        memoryUsage: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
      }));
    }
  }, []);

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({ ...prev, fps: frameCount }));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };

    const rafId = requestAnimationFrame(measureFPS);
    const memoryInterval = setInterval(measureMemoryUsage, 5000);

    return () => {
      cancelAnimationFrame(rafId);
      clearInterval(memoryInterval);
    };
  }, [measureMemoryUsage]);

  return {
    metrics,
    measureRenderTime,
    updateEventCount,
  };
}
