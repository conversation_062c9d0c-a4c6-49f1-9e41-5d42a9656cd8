export const siteConfig = {
  name: "Interactive Historical Timeline",
  description: "Explore world history through an interactive timeline interface. Discover historical events across different regions and time periods with rich contextual information.",
  url: "https://interactive-historical-timeline.vercel.app",
  ogImage: "https://interactive-historical-timeline.vercel.app/og.jpg",
  links: {
    twitter: "https://twitter.com/historytimeline",
    github: "https://github.com/username/interactive-historical-timeline",
  },
  creator: {
    name: "Historical Timeline Team", 
    twitter: "@historytimeline",
  },
  keywords: [
    "history",
    "timeline",
    "historical events",
    "world history",
    "interactive",
    "education",
    "chronology",
    "historical data",
  ],
  authors: [
    {
      name: "Historical Timeline Team",
      url: "https://interactive-historical-timeline.vercel.app",
    },
  ],
  // Theme configuration
  theme: {
    defaultTheme: "system" as const,
    enableSystem: true,
    disableTransitionOnChange: true,
  },
  // Analytics configuration
  analytics: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID,
  },
  // Advertisement configuration
  ads: {
    googleAdsenseId: process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_ID,
    enableAds: process.env.NODE_ENV === "production",
  },
  // Timeline configuration
  timeline: {
    defaultZoomLevel: "decade" as const,
    defaultYear: 1066,
    minYear: -3000,
    maxYear: 2024,
    enableVirtualization: true,
    eventsPerPage: 50,
  },
  // Map configuration
  map: {
    enableHistoricalMaps: true,
    defaultMapStyle: "historical" as const,
    enableTerritoryHighlighting: true,
  },
  // Performance configuration
  performance: {
    enableLazyLoading: true,
    imageOptimization: true,
    enableServiceWorker: true,
  },
} as const

export type SiteConfig = typeof siteConfig
