import { Event } from './event';
import { TimelineState } from './timeline';

export interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface TimelineProps extends ComponentProps {
  events: Event[];
  currentYear: number;
  zoomLevel: TimelineState['zoomLevel'];
  onEventSelect: (event: Event) => void;
  onTimeChange: (year: number) => void;
  onZoomChange: (zoomLevel: TimelineState['zoomLevel']) => void;
}

export interface EventCardProps extends ComponentProps {
  event: Event;
  isSelected: boolean;
  onSelect: () => void;
  onHover: (event: Event | null) => void;
  size: 'small' | 'medium' | 'large';
  showDetails?: boolean;
}

export interface EventDetailsProps extends ComponentProps {
  event: Event;
  onClose: () => void;
  onNavigate?: (eventId: string) => void;
  variant?: 'modal' | 'sidebar' | 'inline';
}

export interface SearchBarProps extends ComponentProps {
  onSearch: (query: string) => void;
  onResultSelect: (event: Event) => void;
  placeholder?: string;
  showSuggestions?: boolean;
  showHistory?: boolean;
}

export interface FilterPanelProps extends ComponentProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  facets?: SearchFacets;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface ZoomControlsProps extends ComponentProps {
  currentZoom: TimelineState['zoomLevel'];
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomToFit: () => void;
  canZoomIn: boolean;
  canZoomOut: boolean;
}

export interface TimelineRulerProps extends ComponentProps {
  startYear: number;
  endYear: number;
  currentYear: number;
  zoomLevel: TimelineState['zoomLevel'];
  onYearClick: (year: number) => void;
  showLabels?: boolean;
}

export interface EventLanesProps extends ComponentProps {
  events: Event[];
  regions: Region[];
  onRegionToggle: (regionId: string) => void;
  visibleRegions: string[];
  laneHeight?: number;
}

export interface HistoricalMapProps extends ComponentProps {
  year: number;
  selectedEvent?: Event;
  onEventSelect?: (event: Event) => void;
  showTerritories?: boolean;
  showCities?: boolean;
  showTradeRoutes?: boolean;
}

export interface EventCarouselProps extends ComponentProps {
  events: Event[];
  selectedIndex: number;
  onEventChange: (index: number) => void;
  showThumbnails?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export interface ThemeToggleProps extends ComponentProps {
  variant?: 'button' | 'switch' | 'dropdown';
  showLabel?: boolean;
}

export interface LoadingSpinnerProps extends ComponentProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'spinner' | 'dots' | 'bars';
}

export interface ErrorBoundaryProps extends ComponentProps {
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export interface VirtualizedListProps<T> extends ComponentProps {
  items: T[];
  itemHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number;
  onScroll?: (scrollTop: number) => void;
}

// Import types from other files to avoid circular dependencies
import { SearchFilters, SearchFacets } from './search';
import { Region } from './region';
