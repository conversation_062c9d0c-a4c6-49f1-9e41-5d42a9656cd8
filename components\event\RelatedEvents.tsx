"use client"

import React from 'react';
import { Event } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowRight, ArrowLeft, ArrowUpDown, Calendar, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RelatedEventsProps {
  currentEvent: Event;
  allEvents: Event[];
  onEventSelect: (event: Event) => void;
  maxRelated?: number;
  className?: string;
}

export function RelatedEvents({
  currentEvent,
  allEvents,
  onEventSelect,
  maxRelated = 5,
  className,
}: RelatedEventsProps) {
  // Find related events based on connections
  const getRelatedEvents = () => {
    const related = {
      causes: [] as Event[],
      consequences: [] as Event[],
      related: [] as Event[],
      simultaneous: [] as Event[],
    };

    // Get events by ID from connections
    if (currentEvent.connections.causes) {
      related.causes = currentEvent.connections.causes
        .map(id => allEvents.find(e => e.id === id))
        .filter(Boolean) as Event[];
    }

    if (currentEvent.connections.consequences) {
      related.consequences = currentEvent.connections.consequences
        .map(id => allEvents.find(e => e.id === id))
        .filter(Boolean) as Event[];
    }

    if (currentEvent.connections.related) {
      related.related = currentEvent.connections.related
        .map(id => allEvents.find(e => e.id === id))
        .filter(Boolean) as Event[];
    }

    // Find simultaneous events (same year, different events)
    const currentYear = parseInt(currentEvent.date.start.split('-')[0]);
    related.simultaneous = allEvents
      .filter(event => {
        if (event.id === currentEvent.id) return false;
        const eventYear = parseInt(event.date.start.split('-')[0]);
        return Math.abs(eventYear - currentYear) <= 5; // Within 5 years
      })
      .slice(0, maxRelated);

    return related;
  };

  const relatedEvents = getRelatedEvents();

  const formatDate = (dateStr: string) => {
    const year = parseInt(dateStr.split('-')[0]);
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  const EventCard = ({ event, relationshipType }: { event: Event; relationshipType: string }) => (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow"
      onClick={() => onEventSelect(event)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-sm font-medium line-clamp-2">
            {event.title}
          </CardTitle>
          <Badge variant="outline" className="text-xs ml-2 flex-shrink-0">
            {relationshipType}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            {formatDate(event.date.start)}
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <MapPin className="h-3 w-3" />
            {event.location.country}
          </div>
          <p className="text-xs text-muted-foreground line-clamp-2">
            {event.description.short}
          </p>
        </div>
      </CardContent>
    </Card>
  );

  const hasAnyRelated = 
    relatedEvents.causes.length > 0 ||
    relatedEvents.consequences.length > 0 ||
    relatedEvents.related.length > 0 ||
    relatedEvents.simultaneous.length > 0;

  if (!hasAnyRelated) {
    return (
      <div className={cn("text-center py-8", className)}>
        <p className="text-muted-foreground">No related events found</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Causes */}
      {relatedEvents.causes.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4 text-muted-foreground" />
            <h4 className="font-semibold text-sm">Causes</h4>
            <Badge variant="secondary" className="text-xs">
              {relatedEvents.causes.length}
            </Badge>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {relatedEvents.causes.slice(0, maxRelated).map((event) => (
              <EventCard 
                key={event.id} 
                event={event} 
                relationshipType="Cause"
              />
            ))}
          </div>
        </div>
      )}

      {relatedEvents.causes.length > 0 && relatedEvents.consequences.length > 0 && <Separator />}

      {/* Consequences */}
      {relatedEvents.consequences.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
            <h4 className="font-semibold text-sm">Consequences</h4>
            <Badge variant="secondary" className="text-xs">
              {relatedEvents.consequences.length}
            </Badge>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {relatedEvents.consequences.slice(0, maxRelated).map((event) => (
              <EventCard 
                key={event.id} 
                event={event} 
                relationshipType="Result"
              />
            ))}
          </div>
        </div>
      )}

      {(relatedEvents.causes.length > 0 || relatedEvents.consequences.length > 0) && 
       relatedEvents.related.length > 0 && <Separator />}

      {/* Related Events */}
      {relatedEvents.related.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            <h4 className="font-semibold text-sm">Related Events</h4>
            <Badge variant="secondary" className="text-xs">
              {relatedEvents.related.length}
            </Badge>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {relatedEvents.related.slice(0, maxRelated).map((event) => (
              <EventCard 
                key={event.id} 
                event={event} 
                relationshipType="Related"
              />
            ))}
          </div>
        </div>
      )}

      {relatedEvents.simultaneous.length > 0 && <Separator />}

      {/* Simultaneous Events */}
      {relatedEvents.simultaneous.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <h4 className="font-semibold text-sm">Around the Same Time</h4>
            <Badge variant="secondary" className="text-xs">
              {relatedEvents.simultaneous.length}
            </Badge>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {relatedEvents.simultaneous.slice(0, maxRelated).map((event) => (
              <EventCard 
                key={event.id} 
                event={event} 
                relationshipType="Simultaneous"
              />
            ))}
          </div>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="flex justify-center pt-4">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Navigate to previous event chronologically
              const currentYear = parseInt(currentEvent.date.start.split('-')[0]);
              const previousEvents = allEvents
                .filter(e => parseInt(e.date.start.split('-')[0]) < currentYear)
                .sort((a, b) => parseInt(b.date.start.split('-')[0]) - parseInt(a.date.start.split('-')[0]));
              
              if (previousEvents.length > 0) {
                onEventSelect(previousEvents[0]);
              }
            }}
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Navigate to next event chronologically
              const currentYear = parseInt(currentEvent.date.start.split('-')[0]);
              const nextEvents = allEvents
                .filter(e => parseInt(e.date.start.split('-')[0]) > currentYear)
                .sort((a, b) => parseInt(a.date.start.split('-')[0]) - parseInt(b.date.start.split('-')[0]));
              
              if (nextEvents.length > 0) {
                onEventSelect(nextEvents[0]);
              }
            }}
            className="flex items-center gap-1"
          >
            Next
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
