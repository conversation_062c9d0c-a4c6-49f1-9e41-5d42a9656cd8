"use client"

import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Home, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationControlsProps {
  currentYear: number;
  onYearChange: (year: number) => void;
  onGoToToday: () => void;
  onPanLeft: () => void;
  onPanRight: () => void;
  className?: string;
}

export function NavigationControls({
  currentYear,
  onYearChange,
  onGoToToday,
  onPanLeft,
  onPanRight,
  className,
}: NavigationControlsProps) {
  const handleYearInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const year = parseInt(e.target.value);
    if (!isNaN(year)) {
      onYearChange(year);
    }
  };

  const formatYear = (year: number) => {
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  return (
    <div className={cn("flex flex-col sm:flex-row items-start sm:items-center gap-2", className)}>
      {/* Pan Controls */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={onPanLeft}
          title="Pan Left"
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onPanRight}
          title="Pan Right"
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>
      </div>

      {/* Current Year Display and Input */}
      <div className="flex items-center gap-1 sm:gap-2">
        <span className="text-xs sm:text-sm text-muted-foreground">Year:</span>
        <input
          type="number"
          value={currentYear}
          onChange={handleYearInput}
          className="w-16 sm:w-20 px-1 sm:px-2 py-1 text-xs sm:text-sm border rounded bg-background text-foreground"
          title="Enter year to navigate to"
        />
        <span className="text-xs sm:text-sm text-muted-foreground hidden sm:inline">
          ({formatYear(currentYear)})
        </span>
      </div>

      {/* Quick Navigation */}
      <div className="flex items-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={onGoToToday}
          title="Go to Current Year"
          className="h-8 px-1 sm:px-2 text-xs"
        >
          <Calendar className="h-3 w-3 sm:mr-1" />
          <span className="hidden sm:inline">Today</span>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onYearChange(0)}
          title="Go to Year 1 CE"
          className="h-8 px-1 sm:px-2 text-xs"
        >
          <Home className="h-3 w-3 sm:mr-1" />
          <span className="hidden sm:inline">Origin</span>
        </Button>
      </div>
    </div>
  );
}
