"use client"

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FocusIndicatorProps {
  className?: string;
  showKeyboardHint?: boolean;
}

export function FocusIndicator({ className, showKeyboardHint = true }: FocusIndicatorProps) {
  const [isKeyboardUser, setIsKeyboardUser] = useState(false);
  const [lastKeyPressed, setLastKeyPressed] = useState<string | null>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setIsKeyboardUser(true);
      setLastKeyPressed(e.key);
      
      // Hide the key after a short delay
      setTimeout(() => setLastKeyPressed(null), 1000);
    };

    const handleMouseDown = () => {
      setIsKeyboardUser(false);
      setLastKeyPressed(null);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  if (!isKeyboardUser || !showKeyboardHint) return null;

  return (
    <div className={cn("fixed top-4 right-4 z-50 pointer-events-none", className)}>
      <AnimatePresence>
        {lastKeyPressed && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            className="bg-primary text-primary-foreground px-3 py-2 rounded-lg shadow-lg text-sm font-mono"
          >
            Key: {lastKeyPressed}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Global CSS for better focus indicators
export const focusStyles = `
  /* Enhanced focus styles for keyboard navigation */
  .focus-visible:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Custom focus ring for interactive elements */
  [data-event-id]:focus-visible {
    outline: 3px solid hsl(var(--primary));
    outline-offset: 3px;
    box-shadow: 0 0 0 1px hsl(var(--background)), 0 0 0 4px hsl(var(--primary) / 0.3);
  }

  /* Focus styles for buttons and controls */
  button:focus-visible,
  [role="button"]:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Focus styles for input elements */
  input:focus-visible,
  textarea:focus-visible,
  select:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 1px;
  }

  /* Skip to content link */
  .skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-to-content:focus {
    top: 6px;
  }

  /* Keyboard navigation hints */
  .keyboard-hint {
    position: relative;
  }

  .keyboard-hint::after {
    content: attr(data-keyboard-hint);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: hsl(var(--popover));
    color: hsl(var(--popover-foreground));
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    border: 1px solid hsl(var(--border));
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .keyboard-hint:focus-visible::after {
    opacity: 1;
  }
`;
