"use client"

import React, { useState, useEffect, useRef } from 'react';
import { Event, TimelineState } from '@/lib/types';
import { getAllEvents } from '@/lib/data/loader';
import { getTimelineConfig } from '@/lib/utils/site';
import { EventCard } from './EventCard';
import { TimelineRuler } from './TimelineRuler';
import { ZoomControls } from './ZoomControls';
import { NavigationControls } from './NavigationControls';
import { EventDetails } from '../event/EventDetails';
import { SearchBar } from '../search/SearchBar';
import { FilterPanel } from '../filters/FilterPanel';
import { HistoricalMap, MapOverlay } from '../map';
import { useFilters } from '@/lib/hooks/useFilters';
import { useKeyboardNavigation } from '@/lib/hooks/useKeyboardNavigation';
import { KeyboardShortcutsHelp } from '@/components/ui/KeyboardShortcutsHelp';
import { FocusIndicator } from '@/components/ui/FocusIndicator';
import { SkipToContent } from '@/components/ui/SkipToContent';
import { KeyboardNavigationStatus } from '@/components/ui/KeyboardNavigationStatus';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Map, Clock, Keyboard } from 'lucide-react';

interface TimelineProps {
  className?: string;
}

export function Timeline({ className }: TimelineProps) {
  const timelineRef = useRef<HTMLDivElement>(null);
  const config = getTimelineConfig();
  
  const [timelineState, setTimelineState] = useState<TimelineState>({
    currentYear: config.defaultYear,
    zoomLevel: config.defaultZoomLevel,
    selectedEvent: null,
    visibleRegions: ['Europe', 'Asia', 'Africa', 'North America'],
    filterCategories: [],
    viewMode: 'timeline',
    isLoading: true,
    searchQuery: '',
    simultaneousEvents: [],
  });

  const [events, setEvents] = useState<Event[]>([]);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [viewportWidth, setViewportWidth] = useState(1200);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [showMapView, setShowMapView] = useState(false);
  const [selectedMapEvent, setSelectedMapEvent] = useState<Event | null>(null);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);
  const [isKeyboardActive, setIsKeyboardActive] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Initialize filters
  const {
    filters,
    filteredEvents,
    updateFilters,
    hasActiveFilters,
    activeFilterCount,
  } = useFilters(events);

  // Load events on component mount
  useEffect(() => {
    const loadEvents = async () => {
      try {
        const allEvents = getAllEvents();
        setEvents(allEvents);
        setTimelineState(prev => ({ ...prev, isLoading: false }));
      } catch (error) {
        console.error('Failed to load events:', error);
        setTimelineState(prev => ({ ...prev, isLoading: false }));
      }
    };

    loadEvents();
  }, []);

  // Handle horizontal scrolling
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollPosition(e.currentTarget.scrollLeft);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return; // Don't interfere with input fields

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          handlePanLeft();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handlePanRight();
          break;
        case '+':
        case '=':
          e.preventDefault();
          handleZoomIn();
          break;
        case '-':
          e.preventDefault();
          handleZoomOut();
          break;
        case 'Home':
          e.preventDefault();
          handleTimeChange(0);
          break;
        case 'End':
          e.preventDefault();
          handleTimeChange(new Date().getFullYear());
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [timelineState.zoomLevel]);

  // Handle viewport resize
  useEffect(() => {
    const handleResize = () => {
      if (timelineRef.current) {
        setViewportWidth(timelineRef.current.clientWidth);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate zoom levels and positioning
  const zoomLevels = {
    century: { pixelsPerYear: 2, yearRange: 100 },
    decade: { pixelsPerYear: 20, yearRange: 50 },
    year: { pixelsPerYear: 200, yearRange: 10 },
    month: { pixelsPerYear: 2400, yearRange: 1 },
    day: { pixelsPerYear: 87600, yearRange: 0.1 },
  };

  const currentZoom = zoomLevels[timelineState.zoomLevel];
  const startYear = timelineState.currentYear - currentZoom.yearRange;
  const endYear = timelineState.currentYear + currentZoom.yearRange;

  // Filter events based on current view and active filters
  const visibleEvents = filteredEvents.filter(event => {
    const eventYear = parseInt(event.date.start.split('-')[0]);
    const isInTimeRange = eventYear >= startYear && eventYear <= endYear;
    const isInVisibleRegion = timelineState.visibleRegions.includes(event.location.region);
    return isInTimeRange && isInVisibleRegion;
  });

  // Calculate event positions with collision detection
  const eventsWithPositions = visibleEvents.map((event) => {
    const eventYear = parseInt(event.date.start.split('-')[0]);
    const x = (eventYear - startYear) * currentZoom.pixelsPerYear;

    // Assign lanes based on region
    const regionLanes = {
      'Europe': 0,
      'Asia': 1,
      'Africa': 2,
      'North America': 3,
    };

    const lane = regionLanes[event.location.region as keyof typeof regionLanes] || 0;
    // Responsive lane heights: mobile: 80px, tablet: 100px, desktop: 120px
    const laneHeight = viewportWidth < 768 ? 80 : viewportWidth < 1024 ? 100 : 120;
    const y = lane * laneHeight + 60;

    return {
      ...event,
      position: { x, y, year: eventYear, lane },
      isVisible: x >= -200 && x <= (viewportWidth || 1200) + 200, // Only render visible events
      isSelected: timelineState.selectedEvent?.id === event.id,
      isHighlighted: false,
    };
  });

  const handleEventSelect = (event: Event) => {
    setTimelineState(prev => ({
      ...prev,
      selectedEvent: event,
    }));
    setShowEventDetails(true);
  };

  const handleCloseEventDetails = () => {
    setShowEventDetails(false);
  };

  const handleMapEventSelect = (event: Event) => {
    setSelectedMapEvent(event);
    setTimelineState(prev => ({
      ...prev,
      selectedEvent: event,
    }));
    setShowEventDetails(true);
  };

  const handleMapRegionSelect = (region: string) => {
    // Filter events by region
    updateFilters({ regions: [region] });
  };

  const handleToggleMapView = () => {
    setShowMapView(prev => !prev);
  };

  // Keyboard navigation handlers
  const handleFocusSearch = () => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const handleShowKeyboardHelp = () => {
    setShowKeyboardHelp(true);
  };

  const handleCloseKeyboardHelp = () => {
    setShowKeyboardHelp(false);
  };

  const handleRandomEvent = () => {
    if (filteredEvents.length > 0) {
      const randomIndex = Math.floor(Math.random() * filteredEvents.length);
      handleEventSelect(filteredEvents[randomIndex]);
    }
  };

  const handleCloseModal = () => {
    if (showEventDetails) {
      setShowEventDetails(false);
    }
    if (showKeyboardHelp) {
      setShowKeyboardHelp(false);
    }
  };

  const handleTimeChange = (year: number) => {
    setTimelineState(prev => ({
      ...prev,
      currentYear: year,
    }));
  };

  const handleZoomChange = (zoomLevel: TimelineState['zoomLevel']) => {
    setTimelineState(prev => ({
      ...prev,
      zoomLevel,
    }));
  };

  const handleZoomIn = () => {
    const levels: TimelineState['zoomLevel'][] = ['century', 'decade', 'year', 'month', 'day'];
    const currentIndex = levels.indexOf(timelineState.zoomLevel);
    if (currentIndex < levels.length - 1) {
      handleZoomChange(levels[currentIndex + 1]);
    }
  };

  const handleZoomOut = () => {
    const levels: TimelineState['zoomLevel'][] = ['century', 'decade', 'year', 'month', 'day'];
    const currentIndex = levels.indexOf(timelineState.zoomLevel);
    if (currentIndex > 0) {
      handleZoomChange(levels[currentIndex - 1]);
    }
  };

  const handleZoomToFit = () => {
    // Calculate optimal zoom level based on available events
    const eventYears = events.map(event => parseInt(event.date.start.split('-')[0]));
    if (eventYears.length === 0) return;

    const minYear = Math.min(...eventYears);
    const maxYear = Math.max(...eventYears);
    const range = maxYear - minYear;

    let optimalZoom: TimelineState['zoomLevel'] = 'decade';
    if (range > 500) optimalZoom = 'century';
    else if (range > 50) optimalZoom = 'decade';
    else if (range > 5) optimalZoom = 'year';
    else optimalZoom = 'month';

    const centerYear = Math.floor((minYear + maxYear) / 2);
    setTimelineState(prev => ({
      ...prev,
      zoomLevel: optimalZoom,
      currentYear: centerYear,
    }));
  };

  const handlePanLeft = () => {
    const panAmount = currentZoom.yearRange * 0.5;
    handleTimeChange(timelineState.currentYear - panAmount);
  };

  const handlePanRight = () => {
    const panAmount = currentZoom.yearRange * 0.5;
    handleTimeChange(timelineState.currentYear + panAmount);
  };

  // Initialize keyboard navigation
  const { setModalOpen } = useKeyboardNavigation({
    events: filteredEvents,
    selectedEvent: timelineState.selectedEvent,
    timelineState,
    onEventSelect: handleEventSelect,
    onNavigateLeft: handlePanLeft,
    onNavigateRight: handlePanRight,
    onZoomIn: handleZoomIn,
    onZoomOut: handleZoomOut,
    onZoomToFit: handleZoomToFit,
    onToggleMap: handleToggleMapView,
    onFocusSearch: handleFocusSearch,
    onShowHelp: handleShowKeyboardHelp,
    onRandomEvent: handleRandomEvent,
    onCloseModal: handleCloseModal,
    enabled: true,
  });

  // Track modal state for keyboard navigation
  useEffect(() => {
    setModalOpen(showEventDetails || showKeyboardHelp);
  }, [showEventDetails, showKeyboardHelp, setModalOpen]);

  // Track keyboard activity
  useEffect(() => {
    const handleKeyDown = () => setIsKeyboardActive(true);
    const handleMouseDown = () => setIsKeyboardActive(false);

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  const canZoomIn = timelineState.zoomLevel !== 'day';
  const canZoomOut = timelineState.zoomLevel !== 'century';

  if (timelineState.isLoading) {
    return (
      <div className={cn("flex items-center justify-center h-96", className)}>
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={cn("relative w-full h-full bg-background", className)}>
      {/* Skip to Content Link */}
      <SkipToContent targetId="timeline-content" />

      {/* Focus Indicator for Keyboard Users */}
      <FocusIndicator />

      {/* Timeline Header */}
      <div className="flex flex-col p-4 border-b gap-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <h1 className="text-xl md:text-2xl font-bold">Historical Timeline</h1>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            <div className="w-full sm:w-auto">
              <NavigationControls
                currentYear={timelineState.currentYear}
                onYearChange={handleTimeChange}
                onGoToToday={() => handleTimeChange(new Date().getFullYear())}
                onPanLeft={handlePanLeft}
                onPanRight={handlePanRight}
                className="flex-wrap"
              />
            </div>
            <div className="w-full sm:w-auto">
              <ZoomControls
                currentZoom={timelineState.zoomLevel}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onZoomToFit={handleZoomToFit}
                canZoomIn={canZoomIn}
                canZoomOut={canZoomOut}
                className="flex-wrap"
              />
            </div>
            <div className="w-full sm:w-auto">
              <Button
                variant={showMapView ? "default" : "outline"}
                size="sm"
                onClick={handleToggleMapView}
                className="flex items-center gap-2 keyboard-hint"
                data-keyboard-hint="Press M"
                title={`Switch to ${showMapView ? "Timeline" : "Map"} view (M)`}
              >
                {showMapView ? <Clock className="h-4 w-4" /> : <Map className="h-4 w-4" />}
                {showMapView ? "Timeline" : "Map"}
              </Button>
            </div>
            <div className="w-full sm:w-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={handleShowKeyboardHelp}
                className="flex items-center gap-2 keyboard-hint"
                title="Keyboard shortcuts (H)"
                data-keyboard-hint="Press H or ?"
              >
                <Keyboard className="h-4 w-4" />
                <span className="hidden sm:inline">Shortcuts</span>
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <SearchBar
            ref={searchInputRef}
            events={filteredEvents}
            onEventSelect={handleEventSelect}
            onSearchChange={(query, results) => {
              // Update timeline state with search results if needed
              console.log('Search:', query, results.length, 'results');
            }}
            className="w-full max-w-lg"
          />
          <FilterPanel
            events={events}
            filters={filters}
            onFiltersChange={updateFilters}
          />
        </div>
      </div>

      {/* Timeline Container */}
      <div
        id="timeline-content"
        ref={timelineRef}
        className="relative overflow-x-auto overflow-y-hidden h-[400px] md:h-[500px] lg:h-[600px] bg-muted/20 scroll-smooth"
        onScroll={handleScroll}
        tabIndex={0}
        role="application"
        aria-label="Interactive Historical Timeline"
      >
        {/* Timeline Ruler */}
        <TimelineRuler
          startYear={startYear}
          endYear={endYear}
          currentYear={timelineState.currentYear}
          zoomLevel={timelineState.zoomLevel}
          onYearClick={handleTimeChange}
          pixelsPerYear={currentZoom.pixelsPerYear}
        />

        {/* Events Layer */}
        <div className="relative h-full">
          {eventsWithPositions.map((event) => (
            <EventCard
              key={event.id}
              event={event}
              isSelected={event.isSelected}
              onSelect={() => handleEventSelect(event)}
              onHover={() => {}}
              size="medium"
              style={{
                position: 'absolute',
                left: `${event.position.x}px`,
                top: `${event.position.y}px`,
                transform: 'translateX(-50%)',
              }}
            />
          ))}
        </div>

        {/* Region Labels */}
        <div className="absolute left-0 top-16 w-20 md:w-28 lg:w-32 h-full bg-background/90 border-r">
          {['Europe', 'Asia', 'Africa', 'North America'].map((region, index) => (
            <div
              key={region}
              className="flex items-center justify-center h-[80px] md:h-[100px] lg:h-[120px] text-xs md:text-sm font-medium border-b px-1"
              style={{ backgroundColor: `hsl(${index * 90}, 50%, 95%)` }}
            >
              <span className="text-center leading-tight">
                {region.split(' ').map((word, i) => (
                  <span key={i} className="block md:inline">
                    {word}{i < region.split(' ').length - 1 && <span className="hidden md:inline"> </span>}
                  </span>
                ))}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Map View */}
      {showMapView && (
        <div className="relative h-[400px] md:h-[500px] lg:h-[600px] bg-muted/20">
          <HistoricalMap
            events={filteredEvents}
            selectedEvent={selectedMapEvent}
            timelineState={timelineState}
            onEventSelect={handleMapEventSelect}
            onRegionSelect={handleMapRegionSelect}
            className="w-full h-full"
            showControls={true}
            showEventMarkers={true}
            showRegionHighlights={true}
          />
          <MapOverlay
            selectedEvent={selectedMapEvent}
            onClose={() => setSelectedMapEvent(null)}
          />
        </div>
      )}

      {/* Timeline Info */}
      <div className="p-4 border-t bg-muted/50">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>Showing {visibleEvents.length} events from {startYear} to {endYear}</span>
          <span>Current Year: {timelineState.currentYear} | Zoom: {timelineState.zoomLevel}</span>
        </div>
      </div>

      {/* Event Details Modal/Sidebar */}
      <EventDetails
        event={timelineState.selectedEvent}
        isOpen={showEventDetails}
        onClose={handleCloseEventDetails}
        onNavigate={handleEventSelect}
        allEvents={events}
        variant="modal"
      />

      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp
        isOpen={showKeyboardHelp}
        onClose={handleCloseKeyboardHelp}
      />

      {/* Keyboard Navigation Status */}
      <KeyboardNavigationStatus
        selectedEvent={timelineState.selectedEvent}
        totalEvents={filteredEvents.length}
        currentIndex={timelineState.selectedEvent ? filteredEvents.findIndex(e => e.id === timelineState.selectedEvent?.id) : -1}
        isKeyboardActive={isKeyboardActive}
      />
    </div>
  );
}
