"use client"

import React from 'react';
import { Event } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, SkipBack, Ski<PERSON><PERSON>or<PERSON>, Shuffle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EventNavigationProps {
  currentEvent: Event;
  allEvents: Event[];
  onEventSelect: (event: Event) => void;
  className?: string;
}

export function EventNavigation({
  currentEvent,
  allEvents,
  onEventSelect,
  className,
}: EventNavigationProps) {
  // Sort events chronologically
  const sortedEvents = [...allEvents].sort((a, b) => {
    const yearA = parseInt(a.date.start.split('-')[0]);
    const yearB = parseInt(b.date.start.split('-')[0]);
    return yearA - yearB;
  });

  const currentIndex = sortedEvents.findIndex(event => event.id === currentEvent.id);
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex < sortedEvents.length - 1;

  const navigateToPrevious = () => {
    if (hasPrevious) {
      onEventSelect(sortedEvents[currentIndex - 1]);
    }
  };

  const navigateToNext = () => {
    if (hasNext) {
      onEventSelect(sortedEvents[currentIndex + 1]);
    }
  };

  const navigateToFirst = () => {
    if (sortedEvents.length > 0) {
      onEventSelect(sortedEvents[0]);
    }
  };

  const navigateToLast = () => {
    if (sortedEvents.length > 0) {
      onEventSelect(sortedEvents[sortedEvents.length - 1]);
    }
  };

  const navigateToRandom = () => {
    const randomIndex = Math.floor(Math.random() * sortedEvents.length);
    onEventSelect(sortedEvents[randomIndex]);
  };

  const formatYear = (dateStr: string) => {
    const year = parseInt(dateStr.split('-')[0]);
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Current Event Info */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Badge variant="outline">
            {currentIndex + 1} of {sortedEvents.length}
          </Badge>
          <Badge variant="secondary">
            {formatYear(currentEvent.date.start)}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          {currentEvent.title}
        </p>
      </div>

      {/* Navigation Controls */}
      <div className="flex items-center justify-center gap-1">
        <Button
          variant="outline"
          size="sm"
          onClick={navigateToFirst}
          disabled={currentIndex === 0}
          title="First Event"
        >
          <SkipBack className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={navigateToPrevious}
          disabled={!hasPrevious}
          title="Previous Event"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={navigateToRandom}
          title="Random Event"
        >
          <Shuffle className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={navigateToNext}
          disabled={!hasNext}
          title="Next Event"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={navigateToLast}
          disabled={currentIndex === sortedEvents.length - 1}
          title="Last Event"
        >
          <SkipForward className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Jump Selector */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Jump to Event:</label>
        <Select
          value={currentEvent.id}
          onValueChange={(eventId) => {
            const event = allEvents.find(e => e.id === eventId);
            if (event) {
              onEventSelect(event);
            }
          }}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select an event..." />
          </SelectTrigger>
          <SelectContent className="max-h-60">
            {sortedEvents.map((event) => (
              <SelectItem key={event.id} value={event.id}>
                <div className="flex items-center justify-between w-full">
                  <span className="truncate">{event.title}</span>
                  <Badge variant="outline" className="ml-2 text-xs">
                    {formatYear(event.date.start)}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Era Navigation */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Jump to Era:</label>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Find first ancient event (before 500 CE)
              const ancientEvent = sortedEvents.find(e => 
                parseInt(e.date.start.split('-')[0]) < 500
              );
              if (ancientEvent) onEventSelect(ancientEvent);
            }}
            className="text-xs"
          >
            Ancient
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Find first medieval event (500-1500 CE)
              const medievalEvent = sortedEvents.find(e => {
                const year = parseInt(e.date.start.split('-')[0]);
                return year >= 500 && year < 1500;
              });
              if (medievalEvent) onEventSelect(medievalEvent);
            }}
            className="text-xs"
          >
            Medieval
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Find first modern event (1500-1800 CE)
              const modernEvent = sortedEvents.find(e => {
                const year = parseInt(e.date.start.split('-')[0]);
                return year >= 1500 && year < 1800;
              });
              if (modernEvent) onEventSelect(modernEvent);
            }}
            className="text-xs"
          >
            Early Modern
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Find first contemporary event (1800+ CE)
              const contemporaryEvent = sortedEvents.find(e => 
                parseInt(e.date.start.split('-')[0]) >= 1800
              );
              if (contemporaryEvent) onEventSelect(contemporaryEvent);
            }}
            className="text-xs"
          >
            Modern
          </Button>
        </div>
      </div>
    </div>
  );
}
