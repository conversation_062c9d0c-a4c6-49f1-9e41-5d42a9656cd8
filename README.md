# Interactive Historical Timeline

An interactive web application that visualizes world history through a dynamic timeline interface. Users can explore historical events across different regions and time periods with rich contextual information, dynamic maps, and simultaneous event discovery.

## Features

- 🕒 Interactive timeline with zoom and navigation
- 🌍 Historical maps with dynamic boundaries
- 🎨 Light/dark theme support
- 📱 Responsive design for all devices
- 🔍 Advanced search and filtering
- ⚡ Performance optimized with virtualization
- 🎭 Smooth animations with Framer Motion and GSAP

## Configuration

The website name and other settings can be easily changed through the centralized configuration system located in `lib/config/site.ts`. This allows for easy customization without searching through multiple files.

### Changing the Website Name

To change the website name globally:

1. Open `lib/config/site.ts`
2. Modify the `name` property in the `siteConfig` object
3. The change will be reflected throughout the entire application

### Other Configurable Settings

- Site description and metadata
- Social media links
- Theme settings
- Timeline configuration
- Map settings
- Performance options
- Analytics and advertisement settings