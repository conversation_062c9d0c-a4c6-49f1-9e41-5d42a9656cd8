"use client"

import { useState, useEffect, useMemo } from 'react';
import { Event } from '@/lib/types';
import Fuse from 'fuse.js';

interface SearchFilters {
  categories?: string[];
  regions?: string[];
  timeRange?: {
    start: number;
    end: number;
  };
  significance?: string[];
  eventTypes?: string[];
}

interface SearchResult {
  event: Event;
  score: number;
  matches: Array<{
    key?: string;
    value?: string;
    indices: ReadonlyArray<readonly [number, number]>;
  }>;
}

interface UseSearchOptions {
  threshold?: number;
  maxResults?: number;
  includeScore?: boolean;
  includeMatches?: boolean;
}

export function useSearch(
  events: Event[],
  options: UseSearchOptions = {}
) {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [isSearching, setIsSearching] = useState(false);

  const {
    threshold = 0.4,
    maxResults = 50,
    includeScore = true,
    includeMatches = true,
  } = options;

  // Configure Fuse.js
  const fuse = useMemo(() => {
    const fuseOptions = {
      keys: [
        { name: 'title', weight: 0.3 },
        { name: 'description.short', weight: 0.2 },
        { name: 'description.long', weight: 0.1 },
        { name: 'location.country', weight: 0.1 },
        { name: 'location.region', weight: 0.1 },
        { name: 'location.city', weight: 0.05 },
        { name: 'category.primary', weight: 0.1 },
        { name: 'category.secondary', weight: 0.05 },
        { name: 'category.tags', weight: 0.1 },
        { name: 'figures.name', weight: 0.1 },
        { name: 'figures.role', weight: 0.05 },
      ],
      threshold,
      includeScore,
      includeMatches,
      minMatchCharLength: 2,
      ignoreLocation: false,
      findAllMatches: true,
    };
    return new Fuse(events, fuseOptions);
  }, [events, threshold, includeScore, includeMatches]);

  // Filter events based on filters
  const filteredEvents = useMemo(() => {
    let filtered = events;

    // Apply category filter
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(event =>
        filters.categories!.some(cat =>
          event.category.primary === cat ||
          event.category.secondary?.includes(cat) ||
          event.category.tags.includes(cat)
        )
      );
    }

    // Apply region filter
    if (filters.regions && filters.regions.length > 0) {
      filtered = filtered.filter(event =>
        filters.regions!.includes(event.location.region)
      );
    }

    // Apply time range filter
    if (filters.timeRange) {
      filtered = filtered.filter(event => {
        const eventYear = parseInt(event.date.start.split('-')[0]);
        return eventYear >= filters.timeRange!.start && eventYear <= filters.timeRange!.end;
      });
    }

    // Apply significance filter
    if (filters.significance && filters.significance.length > 0) {
      filtered = filtered.filter(event =>
        filters.significance!.includes(event.significance)
      );
    }

    // Apply event type filter
    if (filters.eventTypes && filters.eventTypes.length > 0) {
      filtered = filtered.filter(event =>
        filters.eventTypes!.includes(event.eventType)
      );
    }

    return filtered;
  }, [events, filters]);

  // Perform search
  const searchResults = useMemo(() => {
    setIsSearching(true);
    
    let results: SearchResult[] = [];
    
    if (query.trim()) {
      // Use filtered events for search
      const fuseOptions = {
        keys: [
          { name: 'title', weight: 0.3 },
          { name: 'description.short', weight: 0.2 },
          { name: 'description.long', weight: 0.1 },
          { name: 'location.country', weight: 0.1 },
          { name: 'location.region', weight: 0.1 },
          { name: 'location.city', weight: 0.05 },
          { name: 'category.primary', weight: 0.1 },
          { name: 'category.secondary', weight: 0.05 },
          { name: 'category.tags', weight: 0.1 },
          { name: 'figures.name', weight: 0.1 },
          { name: 'figures.role', weight: 0.05 },
        ],
        threshold,
        includeScore,
        includeMatches,
        minMatchCharLength: 2,
        ignoreLocation: false,
        findAllMatches: true,
      };
      const searchFuse = new Fuse(filteredEvents, fuseOptions);
      const fuseResults = searchFuse.search(query, { limit: maxResults });

      results = fuseResults.map(result => ({
        event: result.item,
        score: result.score || 0,
        matches: (result.matches || []) as unknown as SearchResult['matches'],
      }));
    } else {
      // Return filtered events without search query
      results = filteredEvents.slice(0, maxResults).map(event => ({
        event,
        score: 0,
        matches: [],
      }));
    }
    
    setIsSearching(false);
    return results;
  }, [query, filteredEvents, threshold, includeScore, includeMatches, maxResults]);

  // Search suggestions based on partial query
  const suggestions = useMemo(() => {
    if (!query.trim() || query.length < 2) return [];
    
    const suggestionFuse = new Fuse(events, {
      keys: ['title', 'category.primary', 'location.country', 'location.region'],
      threshold: 0.3,
      includeScore: true,
      minMatchCharLength: 1,
    });
    
    return suggestionFuse
      .search(query, { limit: 5 })
      .map(result => result.item.title);
  }, [query, events]);

  // Get unique values for filter options
  const filterOptions = useMemo(() => {
    const categories = new Set<string>();
    const regions = new Set<string>();
    const significance = new Set<string>();
    const eventTypes = new Set<string>();
    
    events.forEach(event => {
      categories.add(event.category.primary);
      event.category.secondary?.forEach(cat => categories.add(cat));
      event.category.tags.forEach(tag => categories.add(tag));
      
      regions.add(event.location.region);
      significance.add(event.significance);
      eventTypes.add(event.eventType);
    });
    
    return {
      categories: Array.from(categories).sort(),
      regions: Array.from(regions).sort(),
      significance: Array.from(significance).sort(),
      eventTypes: Array.from(eventTypes).sort(),
    };
  }, [events]);

  // Clear search and filters
  const clearSearch = () => {
    setQuery('');
    setFilters({});
  };

  // Update specific filter
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Remove specific filter
  const removeFilter = (key: keyof SearchFilters) => {
    setFilters(prev => {
      const updated = { ...prev };
      delete updated[key];
      return updated;
    });
  };

  return {
    query,
    setQuery,
    filters,
    setFilters,
    updateFilter,
    removeFilter,
    searchResults,
    suggestions,
    filterOptions,
    isSearching,
    clearSearch,
    hasActiveFilters: Object.keys(filters).length > 0,
    totalResults: searchResults.length,
  };
}
