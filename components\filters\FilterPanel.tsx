"use client"

import React, { useState } from 'react';
import { Event } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Filter, X, Calendar, MapPin, Tag, Star, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FilterState {
  categories: string[];
  regions: string[];
  timeRange: [number, number];
  significance: string[];
  eventTypes: string[];
  searchInDescription: boolean;
}

interface FilterPanelProps {
  events: Event[];
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
}

export function FilterPanel({
  events,
  filters,
  onFiltersChange,
  className,
}: FilterPanelProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Extract unique values from events
  const filterOptions = React.useMemo(() => {
    const categories = new Set<string>();
    const regions = new Set<string>();
    const significance = new Set<string>();
    const eventTypes = new Set<string>();
    let minYear = Infinity;
    let maxYear = -Infinity;

    events.forEach(event => {
      // Categories
      categories.add(event.category.primary);
      event.category.secondary?.forEach(cat => categories.add(cat));
      event.category.tags.forEach(tag => categories.add(tag));

      // Regions
      regions.add(event.location.region);

      // Significance and types
      significance.add(event.significance);
      eventTypes.add(event.eventType);

      // Time range
      const year = parseInt(event.date.start.split('-')[0]);
      minYear = Math.min(minYear, year);
      maxYear = Math.max(maxYear, year);
    });

    return {
      categories: Array.from(categories).sort(),
      regions: Array.from(regions).sort(),
      significance: Array.from(significance).sort(),
      eventTypes: Array.from(eventTypes).sort(),
      timeRange: [minYear === Infinity ? -3000 : minYear, maxYear === -Infinity ? 2024 : maxYear] as [number, number],
    };
  }, [events]);

  const updateFilter = <K extends keyof FilterState>(
    key: K,
    value: FilterState[K]
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const toggleArrayFilter = (
    key: 'categories' | 'regions' | 'significance' | 'eventTypes',
    value: string
  ) => {
    const currentValues = filters[key];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value];
    updateFilter(key, newValues);
  };

  const clearAllFilters = () => {
    onFiltersChange({
      categories: [],
      regions: [],
      timeRange: filterOptions.timeRange,
      significance: [],
      eventTypes: [],
      searchInDescription: false,
    });
  };

  const hasActiveFilters = 
    filters.categories.length > 0 ||
    filters.regions.length > 0 ||
    filters.significance.length > 0 ||
    filters.eventTypes.length > 0 ||
    filters.timeRange[0] !== filterOptions.timeRange[0] ||
    filters.timeRange[1] !== filterOptions.timeRange[1] ||
    filters.searchInDescription;

  const activeFilterCount = 
    filters.categories.length +
    filters.regions.length +
    filters.significance.length +
    filters.eventTypes.length +
    (filters.timeRange[0] !== filterOptions.timeRange[0] || filters.timeRange[1] !== filterOptions.timeRange[1] ? 1 : 0) +
    (filters.searchInDescription ? 1 : 0);

  const formatYear = (year: number) => {
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  return (
    <div className={cn("relative", className)}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "flex items-center gap-2",
              hasActiveFilters && "border-primary bg-primary/5"
            )}
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 min-w-5 text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-80 p-0" align="start">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Filters</h3>
              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="h-auto p-1 text-xs"
                >
                  Clear all
                </Button>
              )}
            </div>
          </div>

          <ScrollArea className="h-96">
            <div className="p-4 space-y-6">
              {/* Time Range */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <label className="text-sm font-medium">Time Period</label>
                </div>
                <div className="space-y-2">
                  <Slider
                    value={filters.timeRange}
                    onValueChange={(value) => updateFilter('timeRange', value as [number, number])}
                    min={filterOptions.timeRange[0]}
                    max={filterOptions.timeRange[1]}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>{formatYear(filters.timeRange[0])}</span>
                    <span>{formatYear(filters.timeRange[1])}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Regions */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <label className="text-sm font-medium">Regions</label>
                </div>
                <div className="space-y-2">
                  {filterOptions.regions.map((region) => (
                    <div key={region} className="flex items-center space-x-2">
                      <Checkbox
                        id={`region-${region}`}
                        checked={filters.regions.includes(region)}
                        onCheckedChange={() => toggleArrayFilter('regions', region)}
                      />
                      <label
                        htmlFor={`region-${region}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {region}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Categories */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  <label className="text-sm font-medium">Categories</label>
                </div>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {filterOptions.categories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category}`}
                        checked={filters.categories.includes(category)}
                        onCheckedChange={() => toggleArrayFilter('categories', category)}
                      />
                      <label
                        htmlFor={`category-${category}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Significance */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  <label className="text-sm font-medium">Significance</label>
                </div>
                <div className="space-y-2">
                  {filterOptions.significance.map((sig) => (
                    <div key={sig} className="flex items-center space-x-2">
                      <Checkbox
                        id={`significance-${sig}`}
                        checked={filters.significance.includes(sig)}
                        onCheckedChange={() => toggleArrayFilter('significance', sig)}
                      />
                      <label
                        htmlFor={`significance-${sig}`}
                        className="text-sm font-normal cursor-pointer capitalize"
                      >
                        {sig}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Event Types */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <label className="text-sm font-medium">Event Types</label>
                </div>
                <div className="space-y-2">
                  {filterOptions.eventTypes.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={filters.eventTypes.includes(type)}
                        onCheckedChange={() => toggleArrayFilter('eventTypes', type)}
                      />
                      <label
                        htmlFor={`type-${type}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {type}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Search Options */}
              <div className="space-y-3">
                <label className="text-sm font-medium">Search Options</label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="search-description"
                    checked={filters.searchInDescription}
                    onCheckedChange={(checked) => 
                      updateFilter('searchInDescription', checked as boolean)
                    }
                  />
                  <label
                    htmlFor="search-description"
                    className="text-sm font-normal cursor-pointer"
                  >
                    Include descriptions in search
                  </label>
                </div>
              </div>
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-2 flex flex-wrap gap-1">
          {filters.categories.map((category) => (
            <Badge
              key={`cat-${category}`}
              variant="secondary"
              className="text-xs cursor-pointer"
              onClick={() => toggleArrayFilter('categories', category)}
            >
              {category}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
          {filters.regions.map((region) => (
            <Badge
              key={`reg-${region}`}
              variant="secondary"
              className="text-xs cursor-pointer"
              onClick={() => toggleArrayFilter('regions', region)}
            >
              {region}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
          {filters.significance.map((sig) => (
            <Badge
              key={`sig-${sig}`}
              variant="secondary"
              className="text-xs cursor-pointer capitalize"
              onClick={() => toggleArrayFilter('significance', sig)}
            >
              {sig}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
          {filters.eventTypes.map((type) => (
            <Badge
              key={`type-${type}`}
              variant="secondary"
              className="text-xs cursor-pointer"
              onClick={() => toggleArrayFilter('eventTypes', type)}
            >
              {type}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
          {(filters.timeRange[0] !== filterOptions.timeRange[0] || 
            filters.timeRange[1] !== filterOptions.timeRange[1]) && (
            <Badge
              variant="secondary"
              className="text-xs cursor-pointer"
              onClick={() => updateFilter('timeRange', filterOptions.timeRange)}
            >
              {formatYear(filters.timeRange[0])} - {formatYear(filters.timeRange[1])}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
