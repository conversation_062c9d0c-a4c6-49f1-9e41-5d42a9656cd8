export interface Region {
  id: string;
  name: string;
  displayName: string;
  description: string;
  color: string;
  isVisible: boolean;
  subregions: Subregion[];
  coordinates: {
    center: {
      lat: number;
      lng: number;
    };
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
  };
  historicalBoundaries: Array<{
    year: number;
    boundaries: GeoJSONFeature;
    name: string;
    description?: string;
  }>;
  eventCount: number;
  timeRange: {
    earliest: number;
    latest: number;
  };
}

export interface Subregion {
  id: string;
  name: string;
  displayName: string;
  parentRegion: string;
  color: string;
  isVisible: boolean;
  coordinates: {
    center: {
      lat: number;
      lng: number;
    };
    bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    };
  };
  eventCount: number;
}

export interface GeoJSONFeature {
  type: 'Feature';
  properties: {
    name: string;
    region: string;
    year: number;
    [key: string]: any;
  };
  geometry: {
    type: 'Polygon' | 'MultiPolygon';
    coordinates: number[][][] | number[][][][];
  };
}

export interface HistoricalTerritory {
  id: string;
  name: string;
  region: string;
  startYear: number;
  endYear?: number;
  boundaries: GeoJSONFeature[];
  capital?: {
    name: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  rulers?: Array<{
    name: string;
    startYear: number;
    endYear?: number;
    title: string;
  }>;
  description: string;
  color: string;
  significance: 'major' | 'moderate' | 'minor';
}

export interface MapConfiguration {
  defaultCenter: {
    lat: number;
    lng: number;
  };
  defaultZoom: number;
  minZoom: number;
  maxZoom: number;
  enableTerritoryHighlighting: boolean;
  enableTradeRoutes: boolean;
  enableCities: boolean;
  style: 'historical' | 'modern' | 'satellite';
  layers: {
    territories: boolean;
    cities: boolean;
    tradeRoutes: boolean;
    battles: boolean;
    borders: boolean;
  };
}

export interface TradeRoute {
  id: string;
  name: string;
  description: string;
  startYear: number;
  endYear?: number;
  path: Array<{
    lat: number;
    lng: number;
    name?: string;
  }>;
  goods: string[];
  significance: 'major' | 'moderate' | 'minor';
  color: string;
}

export interface HistoricalCity {
  id: string;
  name: string;
  modernName?: string;
  region: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  foundedYear?: number;
  destroyedYear?: number;
  population?: Array<{
    year: number;
    count: number;
    estimate: boolean;
  }>;
  significance: 'major' | 'moderate' | 'minor';
  type: 'capital' | 'trade' | 'religious' | 'military' | 'cultural';
  description: string;
}
