"use client"

import React from 'react';
import { TimelineState } from '@/lib/types';
import { cn } from '@/lib/utils';

interface TimelineRulerProps {
  startYear: number;
  endYear: number;
  currentYear: number;
  zoomLevel: TimelineState['zoomLevel'];
  onYearClick: (year: number) => void;
  pixelsPerYear: number;
  showLabels?: boolean;
  className?: string;
}

export function TimelineRuler({
  startYear,
  endYear,
  currentYear,
  zoomLevel,
  onYearClick,
  pixelsPerYear,
  showLabels = true,
  className,
}: TimelineRulerProps) {
  // Calculate tick intervals based on zoom level
  const getTickIntervals = () => {
    switch (zoomLevel) {
      case 'century':
        return { major: 100, minor: 50 };
      case 'decade':
        return { major: 10, minor: 5 };
      case 'year':
        return { major: 1, minor: 0.5 };
      case 'month':
        return { major: 0.1, minor: 0.05 }; // Roughly monthly
      case 'day':
        return { major: 0.01, minor: 0.005 }; // Roughly daily
      default:
        return { major: 10, minor: 5 };
    }
  };

  const { major: majorInterval, minor: minorInterval } = getTickIntervals();
  
  // Generate tick marks
  const generateTicks = () => {
    const ticks: Array<{
      year: number;
      x: number;
      type: 'major' | 'minor';
      label?: string;
    }> = [];

    // Calculate the range and step
    const range = endYear - startYear;
    const totalWidth = range * pixelsPerYear;

    // Generate major ticks
    const majorStart = Math.floor(startYear / majorInterval) * majorInterval;
    for (let year = majorStart; year <= endYear + majorInterval; year += majorInterval) {
      if (year >= startYear - majorInterval && year <= endYear + majorInterval) {
        const x = (year - startYear) * pixelsPerYear;
        ticks.push({
          year,
          x,
          type: 'major',
          label: formatYearLabel(year, zoomLevel),
        });
      }
    }

    // Generate minor ticks (only if zoom level allows)
    if (pixelsPerYear > 10) {
      const minorStart = Math.floor(startYear / minorInterval) * minorInterval;
      for (let year = minorStart; year <= endYear + minorInterval; year += minorInterval) {
        if (year >= startYear - minorInterval && year <= endYear + minorInterval) {
          // Don't add minor tick if there's already a major tick at this position
          const hasMajorTick = ticks.some(tick => 
            tick.type === 'major' && Math.abs(tick.year - year) < 0.001
          );
          
          if (!hasMajorTick) {
            const x = (year - startYear) * pixelsPerYear;
            ticks.push({
              year,
              x,
              type: 'minor',
            });
          }
        }
      }
    }

    return ticks.sort((a, b) => a.year - b.year);
  };

  const formatYearLabel = (year: number, zoomLevel: TimelineState['zoomLevel']) => {
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      switch (zoomLevel) {
        case 'century':
          return `${year} CE`;
        case 'decade':
          return `${year}`;
        case 'year':
          return `${year}`;
        case 'month':
          return `${year}`;
        case 'day':
          return `${year}`;
        default:
          return `${year}`;
      }
    }
  };

  const ticks = generateTicks();
  const currentYearX = (currentYear - startYear) * pixelsPerYear;

  return (
    <div className={cn("relative h-16 bg-background border-b", className)}>
      {/* Background grid */}
      <div className="absolute inset-0 overflow-hidden">
        {ticks.map((tick) => (
          <div
            key={`grid-${tick.year}`}
            className={cn(
              "absolute top-0 bottom-0 border-l",
              tick.type === 'major' 
                ? "border-border" 
                : "border-border/30"
            )}
            style={{ left: `${tick.x}px` }}
          />
        ))}
      </div>

      {/* Current year indicator */}
      <div
        className="absolute top-0 bottom-0 w-0.5 bg-primary z-10"
        style={{ left: `${currentYearX}px` }}
      >
        <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-primary rounded-full" />
      </div>

      {/* Tick marks and labels */}
      <div className="relative h-full">
        {ticks.map((tick) => (
          <div
            key={`tick-${tick.year}`}
            className="absolute top-0 cursor-pointer group"
            style={{ left: `${tick.x}px` }}
            onClick={() => onYearClick(tick.year)}
          >
            {/* Tick mark */}
            <div
              className={cn(
                "w-0.5 bg-foreground/60 group-hover:bg-foreground",
                tick.type === 'major' ? "h-4" : "h-2"
              )}
            />
            
            {/* Label */}
            {tick.label && showLabels && (
              <div className="absolute top-5 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                <span className="text-xs text-muted-foreground group-hover:text-foreground font-medium">
                  {tick.label}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Era indicators for very zoomed out views */}
      {zoomLevel === 'century' && (
        <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-amber-200 via-blue-200 to-green-200 opacity-30" />
      )}
    </div>
  );
}
