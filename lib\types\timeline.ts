import { Event } from './event';

export interface TimelineState {
  currentYear: number;
  zoomLevel: 'century' | 'decade' | 'year' | 'month' | 'day';
  selectedEvent: Event | null;
  visibleRegions: string[];
  filterCategories: string[];
  viewMode: 'timeline' | 'comparative' | 'narrative';
  isLoading: boolean;
  searchQuery: string;
  simultaneousEvents: Event[];
}

export interface TimelineViewport {
  startYear: number;
  endYear: number;
  centerYear: number;
  pixelsPerYear: number;
  width: number;
  height: number;
}

export interface TimelinePosition {
  x: number;
  y: number;
  year: number;
  lane: number;
}

export interface TimelineEvent extends Event {
  position: TimelinePosition;
  isVisible: boolean;
  isSelected: boolean;
  isHighlighted: boolean;
}

export interface TimelineLane {
  id: string;
  region: string;
  color: string;
  isVisible: boolean;
  events: TimelineEvent[];
  height: number;
  yOffset: number;
}

export interface TimelineScale {
  majorTicks: Array<{
    year: number;
    x: number;
    label: string;
  }>;
  minorTicks: Array<{
    year: number;
    x: number;
  }>;
  gridLines: Array<{
    year: number;
    x: number;
    type: 'major' | 'minor';
  }>;
}

export interface TimelineNavigation {
  canGoBack: boolean;
  canGoForward: boolean;
  canZoomIn: boolean;
  canZoomOut: boolean;
  history: Array<{
    year: number;
    zoomLevel: TimelineState['zoomLevel'];
    timestamp: number;
  }>;
}

export interface TimelineAnimation {
  isAnimating: boolean;
  type: 'zoom' | 'pan' | 'select' | 'filter';
  duration: number;
  easing: string;
  progress: number;
}

export interface TimelineInteraction {
  isDragging: boolean;
  isZooming: boolean;
  lastPointerPosition: { x: number; y: number };
  dragStartPosition: { x: number; y: number };
  zoomStartLevel: TimelineState['zoomLevel'];
  zoomStartYear: number;
}

export interface TimelineConfiguration {
  minYear: number;
  maxYear: number;
  defaultYear: number;
  defaultZoomLevel: TimelineState['zoomLevel'];
  zoomLevels: Array<{
    level: TimelineState['zoomLevel'];
    pixelsPerYear: number;
    majorTickInterval: number;
    minorTickInterval: number;
  }>;
  lanes: {
    height: number;
    spacing: number;
    maxVisible: number;
  };
  performance: {
    virtualizeThreshold: number;
    renderBuffer: number;
    maxEventsPerFrame: number;
  };
}
