"use client"

import { useState, useEffect, useMemo } from 'react';
import { Event } from '@/lib/types';

export interface FilterState {
  categories: string[];
  regions: string[];
  timeRange: [number, number];
  significance: string[];
  eventTypes: string[];
  searchInDescription: boolean;
}

interface UseFiltersOptions {
  persistToLocalStorage?: boolean;
  storageKey?: string;
}

export function useFilters(
  events: Event[],
  options: UseFiltersOptions = {}
) {
  const {
    persistToLocalStorage = true,
    storageKey = 'timeline-filters',
  } = options;

  // Calculate default time range from events
  const defaultTimeRange = useMemo(() => {
    if (events.length === 0) return [-3000, 2024] as [number, number];
    
    const years = events.map(event => parseInt(event.date.start.split('-')[0]));
    const minYear = Math.min(...years);
    const maxYear = Math.max(...years);
    
    return [minYear, maxYear] as [number, number];
  }, [events]);

  const defaultFilters: FilterState = {
    categories: [],
    regions: [],
    timeRange: defaultTimeRange,
    significance: [],
    eventTypes: [],
    searchInDescription: false,
  };

  const [filters, setFilters] = useState<FilterState>(defaultFilters);

  // Load filters from localStorage on mount
  useEffect(() => {
    if (persistToLocalStorage && typeof window !== 'undefined') {
      try {
        const saved = localStorage.getItem(storageKey);
        if (saved) {
          const parsedFilters = JSON.parse(saved);
          setFilters({
            ...defaultFilters,
            ...parsedFilters,
            timeRange: parsedFilters.timeRange || defaultTimeRange,
          });
        }
      } catch (error) {
        console.error('Failed to load filters from localStorage:', error);
      }
    }
  }, [persistToLocalStorage, storageKey, defaultTimeRange]);

  // Save filters to localStorage when they change
  useEffect(() => {
    if (persistToLocalStorage && typeof window !== 'undefined') {
      try {
        localStorage.setItem(storageKey, JSON.stringify(filters));
      } catch (error) {
        console.error('Failed to save filters to localStorage:', error);
      }
    }
  }, [filters, persistToLocalStorage, storageKey]);

  // Apply filters to events
  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      // Time range filter
      const eventYear = parseInt(event.date.start.split('-')[0]);
      if (eventYear < filters.timeRange[0] || eventYear > filters.timeRange[1]) {
        return false;
      }

      // Region filter
      if (filters.regions.length > 0 && !filters.regions.includes(event.location.region)) {
        return false;
      }

      // Category filter
      if (filters.categories.length > 0) {
        const eventCategories = [
          event.category.primary,
          ...(event.category.secondary || []),
          ...event.category.tags,
        ];
        if (!filters.categories.some(cat => eventCategories.includes(cat))) {
          return false;
        }
      }

      // Significance filter
      if (filters.significance.length > 0 && !filters.significance.includes(event.significance)) {
        return false;
      }

      // Event type filter
      if (filters.eventTypes.length > 0 && !filters.eventTypes.includes(event.eventType)) {
        return false;
      }

      return true;
    });
  }, [events, filters]);

  // Get filter options from all events
  const filterOptions = useMemo(() => {
    const categories = new Set<string>();
    const regions = new Set<string>();
    const significance = new Set<string>();
    const eventTypes = new Set<string>();

    events.forEach(event => {
      categories.add(event.category.primary);
      event.category.secondary?.forEach(cat => categories.add(cat));
      event.category.tags.forEach(tag => categories.add(tag));
      
      regions.add(event.location.region);
      significance.add(event.significance);
      eventTypes.add(event.eventType);
    });

    return {
      categories: Array.from(categories).sort(),
      regions: Array.from(regions).sort(),
      significance: Array.from(significance).sort(),
      eventTypes: Array.from(eventTypes).sort(),
      timeRange: defaultTimeRange,
    };
  }, [events, defaultTimeRange]);

  // Update filters
  const updateFilters = (newFilters: Partial<FilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Update specific filter
  const updateFilter = <K extends keyof FilterState>(
    key: K,
    value: FilterState[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Toggle array filter value
  const toggleArrayFilter = (
    key: 'categories' | 'regions' | 'significance' | 'eventTypes',
    value: string
  ) => {
    setFilters(prev => {
      const currentValues = prev[key];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      return { ...prev, [key]: newValues };
    });
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters(defaultFilters);
  };

  // Clear specific filter
  const clearFilter = (key: keyof FilterState) => {
    setFilters(prev => ({
      ...prev,
      [key]: key === 'timeRange' ? defaultTimeRange : 
             Array.isArray(prev[key]) ? [] : 
             typeof prev[key] === 'boolean' ? false : prev[key],
    }));
  };

  // Check if filters are active
  const hasActiveFilters = useMemo(() => {
    return (
      filters.categories.length > 0 ||
      filters.regions.length > 0 ||
      filters.significance.length > 0 ||
      filters.eventTypes.length > 0 ||
      filters.timeRange[0] !== defaultTimeRange[0] ||
      filters.timeRange[1] !== defaultTimeRange[1] ||
      filters.searchInDescription
    );
  }, [filters, defaultTimeRange]);

  // Count active filters
  const activeFilterCount = useMemo(() => {
    return (
      filters.categories.length +
      filters.regions.length +
      filters.significance.length +
      filters.eventTypes.length +
      (filters.timeRange[0] !== defaultTimeRange[0] || filters.timeRange[1] !== defaultTimeRange[1] ? 1 : 0) +
      (filters.searchInDescription ? 1 : 0)
    );
  }, [filters, defaultTimeRange]);

  // Get active filter summary
  const activeFilterSummary = useMemo(() => {
    const summary: string[] = [];
    
    if (filters.categories.length > 0) {
      summary.push(`${filters.categories.length} categories`);
    }
    if (filters.regions.length > 0) {
      summary.push(`${filters.regions.length} regions`);
    }
    if (filters.significance.length > 0) {
      summary.push(`${filters.significance.length} significance levels`);
    }
    if (filters.eventTypes.length > 0) {
      summary.push(`${filters.eventTypes.length} event types`);
    }
    if (filters.timeRange[0] !== defaultTimeRange[0] || filters.timeRange[1] !== defaultTimeRange[1]) {
      summary.push('custom time range');
    }
    if (filters.searchInDescription) {
      summary.push('description search');
    }
    
    return summary;
  }, [filters, defaultTimeRange]);

  return {
    filters,
    setFilters,
    updateFilters,
    updateFilter,
    toggleArrayFilter,
    clearFilters,
    clearFilter,
    filteredEvents,
    filterOptions,
    hasActiveFilters,
    activeFilterCount,
    activeFilterSummary,
    defaultFilters,
  };
}
