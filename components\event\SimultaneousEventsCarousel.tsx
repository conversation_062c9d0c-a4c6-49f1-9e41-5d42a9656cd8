"use client"

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Event } from '@/lib/types';
import { getSimultaneousEvents } from '@/lib/data/loader';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Clock, MapPin, Calendar, Users, Crown } from 'lucide-react';

interface SimultaneousEventsCarouselProps {
  selectedEvent: Event | null;
  onEventSelect: (event: Event) => void;
  className?: string;
  maxEvents?: number;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export function SimultaneousEventsCarousel({
  selectedEvent,
  onEventSelect,
  className,
  maxEvents = 6,
  autoPlay = false,
  autoPlayInterval = 4000,
}: SimultaneousEventsCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  // Get simultaneous events (now finds nearest events regardless of time gap)
  const simultaneousEvents = useMemo(() => {
    if (!selectedEvent) return [];
    return getSimultaneousEvents(selectedEvent, maxEvents);
  }, [selectedEvent, maxEvents]);

  // Reset index when events change
  useEffect(() => {
    setCurrentIndex(0);
  }, [simultaneousEvents]);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || simultaneousEvents.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % simultaneousEvents.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isAutoPlaying, simultaneousEvents.length, autoPlayInterval]);

  const handlePrevious = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(prev => 
      prev === 0 ? simultaneousEvents.length - 1 : prev - 1
    );
  };

  const handleNext = () => {
    setIsAutoPlaying(false);
    setCurrentIndex(prev => (prev + 1) % simultaneousEvents.length);
  };

  const handleEventClick = (event: Event) => {
    setIsAutoPlaying(false);
    onEventSelect(event);
  };

  const getSignificanceIcon = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return <Crown className="h-4 w-4 text-red-500" />;
      case 'moderate':
        return <Users className="h-4 w-4 text-amber-500" />;
      case 'minor':
        return <MapPin className="h-4 w-4 text-blue-500" />;
      case 'reference':
        return <Calendar className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getSignificanceColor = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return 'border-red-200 bg-red-50 dark:bg-red-950 dark:border-red-800';
      case 'moderate':
        return 'border-amber-200 bg-amber-50 dark:bg-amber-950 dark:border-amber-800';
      case 'minor':
        return 'border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800';
      case 'reference':
        return 'border-gray-200 bg-gray-50 dark:bg-gray-950 dark:border-gray-800';
      default:
        return 'border-gray-200 bg-gray-50 dark:bg-gray-950 dark:border-gray-800';
    }
  };

  const formatTimeGap = (yearGap: number) => {
    if (yearGap === 0) return 'same year';
    if (yearGap === 1) return '1 year apart';
    if (yearGap < 10) return `${yearGap} years apart`;
    if (yearGap < 100) return `${yearGap} years apart`;
    if (yearGap < 1000) return `${Math.round(yearGap / 10) * 10} years apart`;
    return `${Math.round(yearGap / 100) * 100} years apart`;
  };

  if (!selectedEvent || simultaneousEvents.length === 0) {
    return null;
  }

  const currentEvent = simultaneousEvents[currentIndex];

  // Helper function to parse year from date string, handling BCE dates
  const parseYear = (dateString: string): number => {
    if (dateString.startsWith('-')) {
      // BCE date: "-753-04-21" -> -753
      return parseInt(dateString.split('-')[1]) * -1;
    } else {
      // CE date: "1066-10-14" -> 1066
      return parseInt(dateString.split('-')[0]);
    }
  };

  const selectedYear = parseYear(selectedEvent.date.start);

  // Helper function to calculate year difference accounting for BCE dates
  const calculateYearDifference = (year1: number, year2: number) => {
    return Math.abs(year1 - year2);
  };

  return (
    <div className={cn("w-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">
            Nearest Events in Time
          </h3>
          <Badge variant="secondary" className="text-xs">
            {simultaneousEvents.length} events
          </Badge>
        </div>
        
        {simultaneousEvents.length > 1 && (
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={handlePrevious}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-muted-foreground px-2">
              {currentIndex + 1} / {simultaneousEvents.length}
            </span>
            <Button
              variant="outline"
              size="icon"
              onClick={handleNext}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Main Event Card */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentEvent.id}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          <Card 
            className={cn(
              "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
              getSignificanceColor(currentEvent.significance)
            )}
            onClick={() => handleEventClick(currentEvent)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-2">
                  {getSignificanceIcon(currentEvent.significance)}
                  <div className="flex-1">
                    <CardTitle className="text-lg leading-tight">
                      {currentEvent.title}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs">
                        {currentEvent.significance}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {currentEvent.category.primary}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Date and Location */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{currentEvent.date.display}</span>
                  <span className="text-muted-foreground">
                    ({formatTimeGap(calculateYearDifference(parseYear(currentEvent.date.start), selectedYear))})
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {currentEvent.location.city && `${currentEvent.location.city}, `}
                    {currentEvent.location.country}
                    {currentEvent.location.region && ` (${currentEvent.location.region})`}
                  </span>
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
                {currentEvent.description.short}
              </p>

              {/* Tags */}
              {currentEvent.category.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {currentEvent.category.tags.slice(0, 4).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {currentEvent.category.tags.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{currentEvent.category.tags.length - 4}
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </AnimatePresence>

      {/* Event Indicators */}
      {simultaneousEvents.length > 1 && (
        <div className="flex justify-center gap-2 mt-4">
          {simultaneousEvents.map((_, index) => (
            <button
              key={index}
              onClick={() => {
                setIsAutoPlaying(false);
                setCurrentIndex(index);
              }}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200",
                index === currentIndex
                  ? "bg-primary scale-125"
                  : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
              )}
            />
          ))}
        </div>
      )}

      {/* Context Information */}
      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
        <div className="text-xs text-muted-foreground text-center">
          Events closest in time to{' '}
          <span className="font-medium text-foreground">
            {selectedEvent.title}
          </span>{' '}
          ({selectedEvent.date.display})
        </div>
      </div>
    </div>
  );
}
