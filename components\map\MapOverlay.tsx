"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { Event, TimelineState } from '@/lib/types';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { X, Calendar, MapPin, Users, Crown } from 'lucide-react';

interface MapOverlayProps {
  selectedEvent: Event | null;
  onClose: () => void;
  className?: string;
}

export function MapOverlay({ selectedEvent, onClose, className }: MapOverlayProps) {
  if (!selectedEvent) return null;

  const getSignificanceIcon = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return <Crown className="h-4 w-4 text-red-500" />;
      case 'moderate':
        return <Users className="h-4 w-4 text-amber-500" />;
      case 'minor':
        return <MapPin className="h-4 w-4 text-blue-500" />;
      case 'reference':
        return <Calendar className="h-4 w-4 text-gray-500" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  const getSignificanceColor = (significance: Event['significance']) => {
    switch (significance) {
      case 'major':
        return 'border-red-500 bg-red-50 dark:bg-red-950';
      case 'moderate':
        return 'border-amber-500 bg-amber-50 dark:bg-amber-950';
      case 'minor':
        return 'border-blue-500 bg-blue-50 dark:bg-blue-950';
      case 'reference':
        return 'border-gray-500 bg-gray-50 dark:bg-gray-950';
      default:
        return 'border-gray-500 bg-gray-50 dark:bg-gray-950';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={cn(
        "absolute top-4 right-4 w-80 max-h-[calc(100vh-2rem)] overflow-y-auto z-10",
        className
      )}
    >
      <Card className={cn("shadow-xl border-2", getSignificanceColor(selectedEvent.significance))}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-2">
              {getSignificanceIcon(selectedEvent.significance)}
              <div className="flex-1">
                <CardTitle className="text-lg leading-tight">
                  {selectedEvent.title}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">
                    {selectedEvent.significance}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {selectedEvent.category.primary}
                  </Badge>
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8 -mt-1 -mr-1"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Date and Location */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">{selectedEvent.date.display}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span>
                {selectedEvent.location.city && `${selectedEvent.location.city}, `}
                {selectedEvent.location.country}
                {selectedEvent.location.region && ` (${selectedEvent.location.region})`}
              </span>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground leading-relaxed">
              {selectedEvent.description.short}
            </p>
          </div>

          {/* Tags */}
          {selectedEvent.category.tags.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Tags
              </div>
              <div className="flex flex-wrap gap-1">
                {selectedEvent.category.tags.slice(0, 6).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {selectedEvent.category.tags.length > 6 && (
                  <Badge variant="outline" className="text-xs">
                    +{selectedEvent.category.tags.length - 6} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Figures */}
          {selectedEvent.figures && selectedEvent.figures.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Key Figures
              </div>
              <div className="space-y-1">
                {selectedEvent.figures.slice(0, 3).map((figure) => (
                  <div key={figure.id} className="flex items-center gap-2 text-sm">
                    <Users className="h-3 w-3 text-muted-foreground" />
                    <span className="font-medium">{figure.name}</span>
                    <span className="text-muted-foreground">({figure.role})</span>
                  </div>
                ))}
                {selectedEvent.figures.length > 3 && (
                  <div className="text-xs text-muted-foreground">
                    +{selectedEvent.figures.length - 3} more figures
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Coordinates */}
          {selectedEvent.location.coordinates && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                Coordinates
              </div>
              <div className="text-xs font-mono bg-muted p-2 rounded">
                {selectedEvent.location.coordinates.lat.toFixed(4)}°N, {' '}
                {selectedEvent.location.coordinates.lng.toFixed(4)}°E
              </div>
            </div>
          )}

          {/* Event Type */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Event Type
            </div>
            <Badge variant="secondary" className="text-xs">
              {selectedEvent.eventType}
            </Badge>
          </div>

          {/* Verification Status */}
          <div className="flex items-center gap-2 pt-2 border-t">
            <div className={cn(
              "w-2 h-2 rounded-full",
              selectedEvent.verified ? "bg-green-500" : "bg-yellow-500"
            )} />
            <span className="text-xs text-muted-foreground">
              {selectedEvent.verified ? "Verified" : "Unverified"} event
            </span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
