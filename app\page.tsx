"use client"

import React, { useState, useEffect } from 'react';
import { Header } from "@/components/layout/Header";
import { Timeline } from "@/components/timeline/Timeline";
import { Event } from '@/lib/types';
import { getAllEvents } from '@/lib/data/loader';

export default function Home() {
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  // Load events on component mount
  useEffect(() => {
    const loadEvents = async () => {
      try {
        const allEvents = await getAllEvents();
        setEvents(allEvents);
      } catch (error) {
        console.error('Failed to load events:', error);
      }
    };

    loadEvents();
  }, []);

  const handleEventSelect = (event: Event) => {
    setSelectedEvent(event);
    // Scroll to timeline or trigger timeline to show this event
    const timelineElement = document.getElementById('timeline-container');
    if (timelineElement) {
      timelineElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleSearchChange = (query: string, results: any[]) => {
    // Handle search changes if needed
    console.log('Search query:', query, 'Results:', results.length);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header
        events={events}
        onEventSelect={handleEventSelect}
        onSearchChange={handleSearchChange}
      />

      <main className="container mx-auto p-2 sm:p-4 lg:p-6">
        <Timeline
          className="w-full"
          selectedEvent={selectedEvent}
          onEventSelect={setSelectedEvent}
        />
      </main>
    </div>
  );
}

