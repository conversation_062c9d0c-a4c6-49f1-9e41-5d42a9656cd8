import { Event } from '@/lib/types/event';
import ancientEvents from './events/ancient.json';
import medievalEvents from './events/medieval.json';
import modernEvents from './events/modern.json';

// Combine all events from different periods
const allEvents: Event[] = [
  ...ancientEvents,
  ...medievalEvents,
  ...modernEvents,
] as Event[];

/**
 * Get all historical events
 */
export function getAllEvents(): Event[] {
  return allEvents;
}

/**
 * Get events by time period
 */
export function getEventsByPeriod(period: 'ancient' | 'medieval' | 'modern'): Event[] {
  switch (period) {
    case 'ancient':
      return ancientEvents as Event[];
    case 'medieval':
      return medievalEvents as Event[];
    case 'modern':
      return modernEvents as Event[];
    default:
      return [];
  }
}

/**
 * Get events by region
 */
export function getEventsByRegion(region: string): Event[] {
  return allEvents.filter(event => event.location.region === region);
}

/**
 * Get events by date range
 */
export function getEventsByDateRange(startYear: number, endYear: number): Event[] {
  return allEvents.filter(event => {
    const eventYear = parseInt(event.date.start.split('-')[0]);
    return eventYear >= startYear && eventYear <= endYear;
  });
}

/**
 * Get event by ID
 */
export function getEventById(id: string): Event | undefined {
  return allEvents.find(event => event.id === id);
}

/**
 * Get event by slug
 */
export function getEventBySlug(slug: string): Event | undefined {
  return allEvents.find(event => event.slug === slug);
}

/**
 * Get related events
 */
export function getRelatedEvents(eventId: string): Event[] {
  const event = getEventById(eventId);
  if (!event) return [];

  const relatedIds = [
    ...(event.connections.causes || []),
    ...(event.connections.consequences || []),
    ...(event.connections.related || []),
  ];

  return relatedIds
    .map(id => getEventById(id))
    .filter((event): event is Event => event !== undefined);
}

/**
 * Get events by category
 */
export function getEventsByCategory(category: string): Event[] {
  return allEvents.filter(event => 
    event.category.primary === category || 
    event.category.secondary?.includes(category)
  );
}

/**
 * Get events by significance
 */
export function getEventsBySignificance(significance: Event['significance']): Event[] {
  return allEvents.filter(event => event.significance === significance);
}

/**
 * Search events by text
 */
export function searchEvents(query: string): Event[] {
  const lowercaseQuery = query.toLowerCase();
  
  return allEvents.filter(event => 
    event.title.toLowerCase().includes(lowercaseQuery) ||
    event.description.short.toLowerCase().includes(lowercaseQuery) ||
    event.description.long.toLowerCase().includes(lowercaseQuery) ||
    event.location.region.toLowerCase().includes(lowercaseQuery) ||
    event.location.country.toLowerCase().includes(lowercaseQuery) ||
    event.category.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
}

/**
 * Get unique regions
 */
export function getUniqueRegions(): string[] {
  const regions = allEvents.map(event => event.location.region);
  return [...new Set(regions)].sort();
}

/**
 * Get unique categories
 */
export function getUniqueCategories(): string[] {
  const categories = allEvents.flatMap(event => [
    event.category.primary,
    ...(event.category.secondary || [])
  ]);
  return [...new Set(categories)].sort();
}

/**
 * Get unique tags
 */
export function getUniqueTags(): string[] {
  const tags = allEvents.flatMap(event => event.category.tags);
  return [...new Set(tags)].sort();
}

/**
 * Get events count by year
 */
export function getEventsCountByYear(): Record<number, number> {
  const counts: Record<number, number> = {};
  
  allEvents.forEach(event => {
    const year = parseInt(event.date.start.split('-')[0]);
    counts[year] = (counts[year] || 0) + 1;
  });
  
  return counts;
}

/**
 * Get date range of all events
 */
export function getDateRange(): { min: number; max: number } {
  const years = allEvents.map(event => parseInt(event.date.start.split('-')[0]));
  return {
    min: Math.min(...years),
    max: Math.max(...years)
  };
}

/**
 * Get simultaneous events (events happening around the same time)
 * Now dynamically finds the nearest events regardless of time gap
 */
export function getSimultaneousEvents(targetEvent: Event, maxEvents: number = 6): Event[] {
  // Helper function to parse year from date string, handling BCE dates
  const parseYear = (dateString: string): number => {
    if (dateString.startsWith('-')) {
      // BCE date: "-753-04-21" -> -753
      return parseInt(dateString.split('-')[1]) * -1;
    } else {
      // CE date: "1066-10-14" -> 1066
      return parseInt(dateString.split('-')[0]);
    }
  };

  const targetYear = parseYear(targetEvent.date.start);

  // Get all other events with their distances
  const eventsWithDistance = allEvents
    .filter(event => event.id !== targetEvent.id) // Exclude the target event itself
    .map(event => {
      const eventYear = parseYear(event.date.start);
      const yearDifference = Math.abs(eventYear - targetYear);

      return {
        event,
        distance: yearDifference,
        year: eventYear
      };
    })
    .sort((a, b) => {
      // Sort by proximity to target year first
      if (a.distance !== b.distance) {
        return a.distance - b.distance;
      }

      // If same distance, sort by significance
      const significanceOrder = { major: 0, moderate: 1, minor: 2, reference: 3 };
      const aSignificance = significanceOrder[a.event.significance];
      const bSignificance = significanceOrder[b.event.significance];

      if (aSignificance !== bSignificance) {
        return aSignificance - bSignificance;
      }

      // If same significance, prefer events from the same region
      if (a.event.location.region === targetEvent.location.region &&
          b.event.location.region !== targetEvent.location.region) {
        return -1;
      }
      if (b.event.location.region === targetEvent.location.region &&
          a.event.location.region !== targetEvent.location.region) {
        return 1;
      }

      // Finally, sort by chronological order
      return a.year - b.year;
    });

  // Return the closest events up to maxEvents
  return eventsWithDistance.slice(0, maxEvents).map(item => item.event);
}

/**
 * Get events within a specific time tolerance (for backward compatibility)
 */
export function getEventsWithinTolerance(targetEvent: Event, tolerance: number = 5): Event[] {
  const targetYear = parseInt(targetEvent.date.start.split('-')[0]);

  return allEvents.filter(event => {
    if (event.id === targetEvent.id) return false;

    const eventYear = parseInt(event.date.start.split('-')[0]);
    const yearDifference = Math.abs(eventYear - targetYear);

    // Adjust tolerance based on date precision
    let adjustedTolerance = tolerance;
    if (targetEvent.date.precision === 'century' || event.date.precision === 'century') {
      adjustedTolerance = 50;
    } else if (targetEvent.date.precision === 'decade' || event.date.precision === 'decade') {
      adjustedTolerance = 10;
    } else if (targetEvent.date.precision === 'year' || event.date.precision === 'year') {
      adjustedTolerance = 5;
    } else if (targetEvent.date.precision === 'month' || event.date.precision === 'month') {
      adjustedTolerance = 1;
    } else if (targetEvent.date.precision === 'day' || event.date.precision === 'day') {
      adjustedTolerance = 1;
    }

    return yearDifference <= adjustedTolerance;
  }).sort((a, b) => {
    const aYear = parseInt(a.date.start.split('-')[0]);
    const bYear = parseInt(b.date.start.split('-')[0]);
    const aDistance = Math.abs(aYear - targetYear);
    const bDistance = Math.abs(bYear - targetYear);

    if (aDistance !== bDistance) {
      return aDistance - bDistance;
    }

    const significanceOrder = { major: 0, moderate: 1, minor: 2, reference: 3 };
    return significanceOrder[a.significance] - significanceOrder[b.significance];
  });
}
