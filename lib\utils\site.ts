import { siteConfig } from "@/lib/config/site"

/**
 * Get the site name
 */
export function getSiteName(): string {
  return siteConfig.name
}

/**
 * Get the site description
 */
export function getSiteDescription(): string {
  return siteConfig.description
}

/**
 * Get the site URL
 */
export function getSiteUrl(): string {
  return siteConfig.url
}

/**
 * Generate page title with site name
 */
export function generatePageTitle(pageTitle?: string): string {
  if (!pageTitle) {
    return siteConfig.name
  }
  return `${pageTitle} | ${siteConfig.name}`
}

/**
 * Generate meta description
 */
export function generateMetaDescription(description?: string): string {
  return description || siteConfig.description
}

/**
 * Get Open Graph image URL
 */
export function getOgImageUrl(path?: string): string {
  if (path) {
    return `${siteConfig.url}${path}`
  }
  return siteConfig.ogImage
}

/**
 * Check if analytics is enabled
 */
export function isAnalyticsEnabled(): boolean {
  return !!siteConfig.analytics.googleAnalyticsId
}

/**
 * Check if ads are enabled
 */
export function areAdsEnabled(): boolean {
  return siteConfig.ads.enableAds && !!siteConfig.ads.googleAdsenseId
}

/**
 * Get timeline configuration
 */
export function getTimelineConfig() {
  return siteConfig.timeline
}

/**
 * Get map configuration
 */
export function getMapConfig() {
  return siteConfig.map
}

/**
 * Get performance configuration
 */
export function getPerformanceConfig() {
  return siteConfig.performance
}
