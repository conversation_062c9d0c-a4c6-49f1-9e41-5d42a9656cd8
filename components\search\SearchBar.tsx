"use client"

import React, { useState, useEffect, useRef, useMemo, forwardRef, useImperativeHandle } from 'react';
import { Event } from '@/lib/types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList } from '@/components/ui/command';
import { Search, X, Clock, MapPin, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import Fuse from 'fuse.js';

interface SearchResult {
  event: Event;
  score: number;
  matches: Array<{
    key?: string;
    value?: string;
    indices: ReadonlyArray<readonly [number, number]>;
  }>;
}

interface SearchBarProps {
  events: Event[];
  onEventSelect: (event: Event) => void;
  onSearchChange?: (query: string, results: SearchResult[]) => void;
  placeholder?: string;
  maxResults?: number;
  showRecentSearches?: boolean;
  className?: string;
}

export const SearchBar = forwardRef<HTMLInputElement, SearchBarProps>(function SearchBar({
  events,
  onEventSelect,
  onSearchChange,
  placeholder = "Search historical events...",
  maxResults = 10,
  showRecentSearches = true,
  className,
}, ref) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Expose the input ref to parent components
  useImperativeHandle(ref, () => inputRef.current!, []);

  // Configure Fuse.js for fuzzy search
  const fuse = useMemo(() => {
    const options = {
      keys: [
        { name: 'title', weight: 0.3 },
        { name: 'description.short', weight: 0.2 },
        { name: 'description.long', weight: 0.1 },
        { name: 'location.country', weight: 0.1 },
        { name: 'location.region', weight: 0.1 },
        { name: 'category.primary', weight: 0.1 },
        { name: 'category.tags', weight: 0.1 },
      ],
      threshold: 0.4,
      includeScore: true,
      includeMatches: true,
      minMatchCharLength: 2,
    };
    return new Fuse(events, options);
  }, [events]);

  // Load recent searches from localStorage
  useEffect(() => {
    if (showRecentSearches) {
      const saved = localStorage.getItem('timeline-recent-searches');
      if (saved) {
        try {
          setRecentSearches(JSON.parse(saved));
        } catch (error) {
          console.error('Failed to load recent searches:', error);
        }
      }
    }
  }, [showRecentSearches]);

  // Save recent searches to localStorage
  const saveRecentSearch = (searchQuery: string) => {
    if (!searchQuery.trim() || !showRecentSearches) return;
    
    const updated = [
      searchQuery,
      ...recentSearches.filter(s => s !== searchQuery)
    ].slice(0, 5);
    
    setRecentSearches(updated);
    localStorage.setItem('timeline-recent-searches', JSON.stringify(updated));
  };

  // Perform search
  const searchResults = useMemo(() => {
    if (!query.trim()) return [];

    const results = fuse.search(query, { limit: maxResults });
    return results.map(result => ({
      event: result.item,
      score: result.score || 0,
      matches: (result.matches || []) as unknown as SearchResult['matches'],
    }));
  }, [query, fuse, maxResults]);

  // Handle search input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setSelectedIndex(-1);
    setIsOpen(true);
    
    if (onSearchChange) {
      const results = newQuery.trim() ? searchResults : [];
      onSearchChange(newQuery, results);
    }
  };

  // Handle event selection
  const handleEventSelect = (event: Event) => {
    onEventSelect(event);
    saveRecentSearch(query);
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.blur();
  };

  // Handle recent search selection
  const handleRecentSearchSelect = (searchQuery: string) => {
    setQuery(searchQuery);
    setIsOpen(true);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    const totalItems = searchResults.length + (showRecentSearches && !query ? recentSearches.length : 0);

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % totalItems);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev - 1 + totalItems) % totalItems);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (query && selectedIndex < searchResults.length) {
            handleEventSelect(searchResults[selectedIndex].event);
          } else if (!query && selectedIndex < recentSearches.length) {
            handleRecentSearchSelect(recentSearches[selectedIndex]);
          }
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Clear search
  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Format date for display
  const formatDate = (dateStr: string) => {
    const year = parseInt(dateStr.split('-')[0]);
    if (year < 0) {
      return `${Math.abs(year)} BCE`;
    } else if (year === 0) {
      return '1 CE';
    } else {
      return `${year} CE`;
    }
  };

  // Highlight search matches
  const highlightMatches = (text: string, matches: ReadonlyArray<readonly [number, number]>) => {
    if (!matches.length) return text;

    let result = '';
    let lastIndex = 0;

    matches.forEach(([start, end]) => {
      result += text.slice(lastIndex, start);
      result += `<mark class="bg-yellow-200 dark:bg-yellow-800">${text.slice(start, end + 1)}</mark>`;
      lastIndex = end + 1;
    });

    result += text.slice(lastIndex);
    return result;
  };

  return (
    <div className={cn("relative w-full max-w-md", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
          className="pl-10 pr-10"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSearch}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Search Results */}
      {isOpen && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          <Command>
            <CommandList>
              {/* Search Results */}
              {query && searchResults.length > 0 && (
                <CommandGroup heading="Search Results">
                  {searchResults.map((result, index) => {
                    const titleMatch = result.matches.find(m => m.key === 'title');
                    const descMatch = result.matches.find(m => m.key === 'description.short');
                    
                    return (
                      <CommandItem
                        key={result.event.id}
                        onSelect={() => handleEventSelect(result.event)}
                        className={cn(
                          "flex flex-col items-start gap-1 p-3 cursor-pointer",
                          selectedIndex === index && "bg-accent"
                        )}
                      >
                        <div className="flex items-center justify-between w-full">
                          <h4 
                            className="font-medium text-sm"
                            dangerouslySetInnerHTML={{
                              __html: titleMatch 
                                ? highlightMatches(result.event.title, titleMatch.indices)
                                : result.event.title
                            }}
                          />
                          <Badge variant="outline" className="text-xs">
                            {result.event.category.primary}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-muted-foreground w-full">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(result.event.date.start)}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {result.event.location.country}
                          </div>
                        </div>
                        
                        {descMatch && (
                          <p 
                            className="text-xs text-muted-foreground line-clamp-2"
                            dangerouslySetInnerHTML={{
                              __html: highlightMatches(result.event.description.short, descMatch.indices)
                            }}
                          />
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              )}

              {/* No Results */}
              {query && searchResults.length === 0 && (
                <CommandEmpty>No events found for "{query}"</CommandEmpty>
              )}

              {/* Recent Searches */}
              {!query && showRecentSearches && recentSearches.length > 0 && (
                <CommandGroup heading="Recent Searches">
                  {recentSearches.map((search, index) => (
                    <CommandItem
                      key={search}
                      onSelect={() => handleRecentSearchSelect(search)}
                      className={cn(
                        "flex items-center gap-2 cursor-pointer",
                        selectedIndex === index && "bg-accent"
                      )}
                    >
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {search}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </div>
      )}
    </div>
  );
});
