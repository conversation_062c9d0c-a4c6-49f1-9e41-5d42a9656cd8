import { Event } from './event';

export interface SearchQuery {
  text: string;
  filters: SearchFilters;
  sortBy: SearchSortOption;
  sortOrder: 'asc' | 'desc';
}

export interface SearchFilters {
  dateRange?: {
    start: number;
    end: number;
  };
  regions?: string[];
  categories?: string[];
  significance?: Array<'major' | 'moderate' | 'minor' | 'reference'>;
  eventTypes?: string[];
  tags?: string[];
  verified?: boolean;
  hasMedia?: boolean;
  hasFigures?: boolean;
}

export interface SearchSortOption {
  field: 'relevance' | 'date' | 'significance' | 'title';
  label: string;
}

export interface SearchResult {
  event: Event;
  score: number;
  matchedFields: Array<{
    field: string;
    value: string;
    highlights: Array<{
      start: number;
      end: number;
    }>;
  }>;
  snippet: string;
}

export interface SearchSuggestion {
  type: 'event' | 'figure' | 'location' | 'category' | 'tag';
  text: string;
  count: number;
  icon?: string;
}

export interface SearchHistory {
  id: string;
  query: string;
  timestamp: number;
  resultCount: number;
}

export interface SearchConfiguration {
  maxResults: number;
  suggestionLimit: number;
  historyLimit: number;
  fuzzyThreshold: number;
  enableHighlighting: boolean;
  enableSuggestions: boolean;
  enableHistory: boolean;
  debounceMs: number;
}

export interface FacetCount {
  value: string;
  count: number;
  selected: boolean;
}

export interface SearchFacets {
  regions: FacetCount[];
  categories: FacetCount[];
  significance: FacetCount[];
  eventTypes: FacetCount[];
  tags: FacetCount[];
  timeRanges: Array<{
    label: string;
    start: number;
    end: number;
    count: number;
    selected: boolean;
  }>;
}
