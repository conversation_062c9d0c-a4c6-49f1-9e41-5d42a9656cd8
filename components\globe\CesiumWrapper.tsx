"use client"

import React from 'react';
import dynamic from 'next/dynamic';
import { Event, TimelineState } from '@/lib/types';
import { Loader2 } from 'lucide-react';

// Dynamic import to avoid SSR issues
const CesiumGlobe = dynamic(() => import('./CesiumGlobe').then(mod => ({ default: mod.CesiumGlobe })), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-muted/20 rounded-lg">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  )
});

interface CesiumWrapperProps {
  events: Event[];
  selectedEvent?: Event | null;
  timelineState?: TimelineState;
  onEventSelect?: (event: Event | null) => void;
  onRegionSelect?: (region: string) => void;
  className?: string;
  showControls?: boolean;
  showEventMarkers?: boolean;
  showRegionHighlights?: boolean;
}

export function CesiumWrapper(props: CesiumWrapperProps) {
  return <CesiumGlobe {...props} />;
}
