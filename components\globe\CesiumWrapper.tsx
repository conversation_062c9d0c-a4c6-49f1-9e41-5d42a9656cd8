"use client"

import React from 'react';
import dynamic from 'next/dynamic';
import { Event, TimelineState } from '@/lib/types';
import { Loader2, Globe } from 'lucide-react';

// Dynamic import to avoid SSR issues
const CesiumGlobe = dynamic(() => import('./CesiumGlobe').then(mod => ({ default: mod.CesiumGlobe })), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-full bg-muted/20 rounded-lg">
      <div className="flex flex-col items-center gap-4">
        <Globe className="h-12 w-12 animate-spin text-primary" />
        <div className="text-center">
          <h3 className="font-semibold">Loading 3D Globe</h3>
          <p className="text-sm text-muted-foreground">
            Initializing Cesium.js...
          </p>
        </div>
      </div>
    </div>
  )
});

interface CesiumWrapperProps {
  events: Event[];
  selectedEvent?: Event | null;
  timelineState?: TimelineState;
  onEventSelect?: (event: Event | null) => void;
  onRegionSelect?: (region: string) => void;
  className?: string;
  showControls?: boolean;
  showEventMarkers?: boolean;
  showRegionHighlights?: boolean;
}

export function CesiumWrapper(props: CesiumWrapperProps) {
  return <CesiumGlobe {...props} />;
}
